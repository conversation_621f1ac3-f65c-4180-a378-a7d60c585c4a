import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { randomInt } from 'crypto';
import {
  GenerateOtpDto,
  OtpPayloadDto,
  ResendOtpDto,
  VerifyOtpDto,
} from './dto/otp.dto';
import { MailerService } from 'src/mailer/mailer.service';
import { render } from '@react-email/components';
import EmailVerification from 'src/template/email-verification';

@Injectable()
export class OtpService {
  constructor(
    private jwtService: JwtService,
    private mailerService: MailerService,
  ) {}

  async generateOTPToken(dto: GenerateOtpDto) {
    const otp = this.generateOTP();
    const expirationTime = this.getExpirationTime();
    const payload: OtpPayloadDto = {
      email: dto.email,
      otp,
      exp: expirationTime,
    };
    const token = await this.jwtService.signAsync(payload, {
      secret: process.env.jwtOtpSecretKey,
    });
    await this.mailerService.sendEmail({
      to: dto.email,
      subject: 'Account Verification',
      html: await render(
        EmailVerification({
          otp,
          verificationUrl: `${process.env.WEBSITE}/auth/verify?token=${token}`,
        }),
      ),
      cc: [],
      from: process.env.EMAIL,
    });
    return {
      status: 'OK',
      token,
    };
  }

  async verifyOTPToken(dto: VerifyOtpDto) {
    try {
      const payload: OtpPayloadDto = await this.jwtService.verifyAsync(
        dto.token,
        {
          secret: process.env.jwtOtpSecretKey,
        },
      );
      if (dto.otp !== payload.otp)
        throw new UnauthorizedException('Invalid OTP');
      if (payload.exp < Math.floor(Date.now() / 1000)) {
        throw new UnauthorizedException('OTP token has expired');
      }
      return {
        status: 'Ok',
        message: 'Email verified',
        email: payload.email,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid OTP token or resend OTP');
    }
  }
  async resendOTP(dto: ResendOtpDto) {
    try {
      // Verify and decode the existing token
      const payload: OtpPayloadDto = await this.jwtService.verifyAsync(
        dto.token,
        {
          secret: process.env.jwtOtpSecretKey,
          ignoreExpiration: true, // Allow expired tokens for resend
        },
      );

      // Generate new OTP and create new payload
      const newOtp = this.generateOTP();
      const expirationTime = this.getExpirationTime();
      const newPayload: OtpPayloadDto = {
        email: payload.email,
        otp: newOtp,
        exp: expirationTime,
      };

      // Generate new token
      const newToken = await this.jwtService.signAsync(newPayload, {
        secret: process.env.jwtOtpSecretKey,
      });

      // Send new OTP email
      await this.mailerService.sendEmail({
        to: payload.email,
        subject: 'Account Verification - New OTP',
        html: await render(
          EmailVerification({
            otp: newOtp,
            verificationUrl: `${process.env.WEBSITE}/auth/verify?token=${newToken}`, // Note: You might need to store URL in initial payload
          }),
        ),
        cc: [],
        from: process.env.EMAIL,
      });

      return {
        status: 'OK',
        token: newToken,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid token or resend OTP');
    }
  }
  private generateOTP(): string {
    return randomInt(100000, 999999).toString();
  }

  private getExpirationTime(): number {
    return Math.floor(Date.now() / 1000) + 600; // 10 minutes from now
  }
}
