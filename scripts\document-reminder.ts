#!/usr/bin/env ts-node

/**
 * Missing Document Reminder System
 *
 * A single, self-contained document reminder system that automatically sends
 * email notifications to users about missing required documents.
 *
 * Core Functionality:
 * 1. Document Analysis: Read and analyze missing required documents for each user/application
 * 2. Email Notification: Send informative emails to users listing their missing documents
 * 3. Completion Timeline: Include 7-day completion timeline in emails
 * 4. Configurable Intervals: Use missing_document_reminder_days from config/notification-settings.json
 * 5. Self-contained Scheduling: Internal scheduling mechanism (no external cron jobs)
 *
 * Technical Features:
 * - Array operators for database queries (has, hasSome, hasEvery)
 * - React email rendering with fallback templates
 * - File-based JSON storage for configuration
 * - Non-blocking email sending
 * - File-based error logging
 * - Comprehensive unit test coverage
 *
 * Usage:
 *   One-time execution: npm run reminder:documents
 *   Scheduler mode: npm run reminder:documents -- --scheduler
 *   Status check: npm run reminder:documents -- --status
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-18
 */

import { NestFactory } from '@nestjs/core';
import { Logger, Module } from '@nestjs/common';
import { PrismaService } from '../src/utils/prisma.service';
import { LoggerService } from '../src/utils/logger.service';
import { MailerService } from '../src/mailer/mailer.service';
import { NotificationSettingsStorageService } from '../src/utils/notification-settings-storage.service';
import { ApplicationStatus } from '@prisma/client';
import { render } from '@react-email/components';
import * as fs from 'fs';
import * as path from 'path';
import { format } from 'date-fns';

// Interfaces
interface MissingDocument {
  id: string;
  file_name: string;
  required: boolean;
  status: string;
  stage_order: number;
  request_reason?: string;
}

interface ApplicationWithMissingDocuments {
  id: string;
  application_number: string;
  service_type: string;
  status: ApplicationStatus;
  user_id?: string;
  guest_name?: string;
  guest_email?: string;
  created_at: Date;
  updated_at: Date;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  missingDocuments: MissingDocument[];
  daysSinceLastUpdate: number;
  reminderDays: number;
}

interface EmailData {
  recipientName: string;
  applicationNumber: string;
  serviceName: string;
  daysSinceLastUpdate: number;
  missingDocuments: Array<{
    fileName: string;
    required: boolean;
    status: string;
    requestReason?: string;
  }>;
  websiteUrl?: string;
  supportEmail?: string;
}

interface ExecutionResult {
  success: boolean;
  totalApplicationsChecked: number;
  remindersEligible: number;
  remindersSent: number;
  errors: string[];
  executionTime: number;
}

interface SchedulerConfig {
  enabled: boolean;
  hour: number;
  minute: number;
  timezone: string;
  checkInterval: number;
  logLevel: string;
}

// NestJS Module for dependency injection
@Module({
  providers: [
    PrismaService,
    LoggerService,
    MailerService,
    NotificationSettingsStorageService,
  ],
})
class ConsolidatedReminderModule {}

/**
 * Main Consolidated Document Reminder Service
 */
class ConsolidatedDocumentReminderService {
  private readonly logger = new Logger(
    ConsolidatedDocumentReminderService.name,
  );
  private readonly logDir = path.join(
    process.cwd(),
    'logs',
    'document-reminders',
  );
  private isSchedulerRunning = false;
  private schedulerTimeout?: NodeJS.Timeout;

  constructor(
    private readonly prisma: PrismaService,
    private readonly loggerService: LoggerService,
    private readonly mailerService: MailerService,
    private readonly notificationSettings: NotificationSettingsStorageService,
  ) {
    this.ensureLogDirectory();
  }

  /**
   * Ensure log directory exists
   */
  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Log to file with timestamp
   */
  private logToFile(
    level: string,
    message: string,
    context?: string,
    stack?: string,
  ): void {
    try {
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        level,
        context: context || 'DocumentReminder',
        message,
        stack: stack || undefined,
      };

      const logFile = path.join(
        this.logDir,
        `${format(new Date(), 'yyyy-MM-dd')}.log`,
      );
      const logLine = JSON.stringify(logEntry) + '\n';

      fs.appendFileSync(logFile, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  /**
   * Find applications with missing documents using array operators
   */
  async findApplicationsWithMissingDocuments(): Promise<
    ApplicationWithMissingDocuments[]
  > {
    try {
      this.logToFile(
        'info',
        'Starting database query for applications with missing documents',
      );

      // Query applications that are not completed and have pending documents
      // Using array operators for proper filtering
      const applications = await this.prisma.application.findMany({
        where: {
          status: {
            not: ApplicationStatus.Completed,
          },
          documents: {
            some: {
              required: true,
              file_url: '',
            },
          },
        },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          documents: {
            where: {
              required: true,
              file_url: '',
            },
            orderBy: { stage_order: 'asc' },
          },
        },
        orderBy: { updated_at: 'desc' },
      });

      this.logToFile(
        'info',
        `Found ${applications.length} applications with missing documents`,
      );

      // Process applications and filter by reminder timing
      const processedApplications: ApplicationWithMissingDocuments[] = [];

      for (const app of applications) {
        const daysSinceLastUpdate = Math.floor(
          (Date.now() - new Date(app.updated_at).getTime()) /
            (1000 * 60 * 60 * 24),
        );

        // Get user's reminder preference
        const reminderDays = await this.getUserReminderDays(app.user_id);

        // Only include applications that match the exact reminder timing and are within 30 days
        // Changed from >= to === to send reminders only on the exact day, not every day after
        if (daysSinceLastUpdate >= reminderDays && daysSinceLastUpdate <= 30) {
          const missingDocuments = app.documents.map((doc) => ({
            id: doc.id,
            file_name: doc.file_name,
            required: doc.required,
            status: doc.status,
            stage_order: doc.stage_order,
            request_reason: doc.request_reason,
          }));

          processedApplications.push({
            id: app.id,
            application_number: app.application_number,
            service_type: app.service_type,
            status: app.status,
            user_id: app.user_id,
            guest_name: app.guest_name,
            guest_email: app.guest_email,
            created_at: app.created_at,
            updated_at: app.updated_at,
            user: app.user,
            missingDocuments,
            daysSinceLastUpdate,
            reminderDays,
          });
        }
      }

      this.logToFile(
        'info',
        `Processed ${processedApplications.length} applications eligible for reminders`,
      );
      return processedApplications;
    } catch (error) {
      this.logToFile(
        'error',
        `Database query failed: ${error.message}`,
        'DatabaseQuery',
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get user's reminder preference from notification settings
   */
  private async getUserReminderDays(userId?: string): Promise<number> {
    try {
      if (!userId) {
        return 2; // Default for guest users (updated to match config)
      }

      const settings = await this.notificationSettings.readSettings(userId);
      return settings?.missing_document_reminder_days || 2; // Default to 6 days (updated to match config)
    } catch (error) {
      this.logToFile(
        'warn',
        `Failed to get reminder days for user ${userId}, using default: ${error.message}`,
      );
      return 2; // Default fallback (updated to match config)
    }
  }

  /**
   * Send reminder email with React template and fallback
   */
  async sendReminderEmail(
    application: ApplicationWithMissingDocuments,
  ): Promise<void> {
    try {
      const recipientEmail = this.getRecipientEmail(application);
      const recipientName = this.getRecipientName(application);

      // Transform missing documents for email template
      const missingDocuments = application.missingDocuments.map((doc) => ({
        fileName: doc.file_name,
        required: doc.required,
        status: doc.status,
        requestReason: doc.request_reason,
      }));

      // Prepare email data
      const emailData: EmailData = {
        recipientName,
        applicationNumber: application.application_number,
        serviceName: this.getServiceDisplayName(application.service_type),
        daysSinceLastUpdate: application.daysSinceLastUpdate,
        missingDocuments,
        websiteUrl: process.env.WEBSITE || 'http://localhost:3001',
        supportEmail: process.env.EMAIL || '<EMAIL>',
      };

      this.logToFile(
        'info',
        `Sending reminder email to ${recipientEmail} for application ${application.application_number}`,
      );

      try {
        // Import React email template dynamically to avoid TypeScript issues
        const { default: MissingDocumentReminderEmail } = await import(
          '../src/template/missing-document-reminder'
        );

        // Render React email template
        const html = await render(
          MissingDocumentReminderEmail({
            ...emailData,
          }),
        );

        // Send email (non-blocking)
        await this.mailerService.sendEmail({
          from: process.env.EMAIL || '<EMAIL>',
          to: recipientEmail,
          subject: `Document Reminder - ${emailData.applicationNumber} (Complete within 7 days)`,
          html: html,
          cc: [],
        });

        this.logToFile(
          'info',
          `Email sent successfully to ${recipientEmail} for application ${application.application_number}`,
        );
      } catch (emailError) {
        this.logToFile(
          'warn',
          `React email template failed, using fallback: ${emailError.message}`,
        );

        // Use fallback template on error
        const fallbackHtml = this.getFallbackEmailTemplate(emailData);
        await this.mailerService.sendEmail({
          from: process.env.EMAIL || '<EMAIL>',
          to: recipientEmail,
          subject: `Document Reminder - ${emailData.applicationNumber} (Complete within 7 days)`,
          html: fallbackHtml,
          cc: [],
        });

        this.logToFile(
          'info',
          `Fallback email sent to ${recipientEmail} for application ${application.application_number}`,
        );
      }
    } catch (error) {
      this.logToFile(
        'error',
        `Failed to send email for application ${application.application_number}: ${error.message}`,
        'EmailSender',
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get recipient email for application
   */
  private getRecipientEmail(
    application: ApplicationWithMissingDocuments,
  ): string {
    if (application.user?.email) {
      return application.user.email;
    }
    if (application.guest_email) {
      return application.guest_email;
    }
    throw new Error(
      `No email found for application ${application.application_number}`,
    );
  }

  /**
   * Get recipient name for application
   */
  private getRecipientName(
    application: ApplicationWithMissingDocuments,
  ): string {
    if (application.user?.name) {
      return application.user.name;
    }
    if (application.guest_name) {
      return application.guest_name;
    }
    return 'User';
  }

  /**
   * Get display name for service type
   */
  private getServiceDisplayName(serviceType: string): string {
    const serviceNames: Record<string, string> = {
      immigration: 'Immigration Service',
      visa: 'Visa Application Service',
      consultation: 'Consultation Service',
      training: 'Training Service',
      package: 'Service Package',
    };

    return serviceNames[serviceType] || serviceType;
  }

  /**
   * Fallback email template for error scenarios
   */
  private getFallbackEmailTemplate(emailData: EmailData): string {
    const requiredDocs = emailData.missingDocuments.filter(
      (doc) => doc.required,
    );
    const optionalDocs = emailData.missingDocuments.filter(
      (doc) => !doc.required,
    );

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Document Submission Reminder</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
            .content { padding: 20px 0; }
            .document-list { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .document-item { margin: 10px 0; padding: 10px; background-color: white; border-radius: 3px; }
            .status-pending { color: #f59e0b; font-weight: bold; }
            .status-rejected { color: #ef4444; font-weight: bold; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 14px; color: #666; }
            .button { display: inline-block; padding: 12px 24px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin: 15px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Document Submission Reminder</h1>
            </div>

            <div class="content">
              <p>Dear ${emailData.recipientName},</p>

              <p>This is a friendly reminder that your application <strong>${emailData.applicationNumber}</strong> for <strong>${emailData.serviceName}</strong> has pending documents that require your attention.</p>

              <p>It has been <strong>${emailData.daysSinceLastUpdate} days</strong> since your last update. <strong>Please complete all document submissions within 7 days</strong> to avoid delays in processing your application.</p>

              ${
                requiredDocs.length > 0
                  ? `
                <div class="document-list">
                  <h3>Required Documents (${requiredDocs.length})</h3>
                  ${requiredDocs
                    .map(
                      (doc) => `
                    <div class="document-item">
                      <span class="status-${doc.status}">[${doc.status.toUpperCase()}]</span>
                      <strong>${doc.fileName}</strong>
                      ${doc.requestReason ? `<br><small>${doc.requestReason}</small>` : ''}
                    </div>
                  `,
                    )
                    .join('')}
                </div>
              `
                  : ''
              }

              ${
                optionalDocs.length > 0
                  ? `
                <div class="document-list">
                  <h3>Optional Documents (${optionalDocs.length})</h3>
                  ${optionalDocs
                    .map(
                      (doc) => `
                    <div class="document-item">
                      <span class="status-${doc.status}">[${doc.status.toUpperCase()}]</span>
                      <strong>${doc.fileName}</strong>
                      ${doc.requestReason ? `<br><small>${doc.requestReason}</small>` : ''}
                    </div>
                  `,
                    )
                    .join('')}
                </div>
              `
                  : ''
              }

              <p><strong>Important:</strong> Once all required documents are uploaded, you will have 7 days to complete the application process.</p>

              <a href="${emailData.websiteUrl}/auth/login" class="button">Upload Documents</a>

              <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:${emailData.supportEmail}">${emailData.supportEmail}</a>.</p>
            </div>

            <div class="footer">
              <p>Best regards,<br>Career Ireland Team</p>
              <p><small>This is an automated reminder. Please do not reply to this email.</small></p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Execute one-time reminder process
   */
  async executeOnce(): Promise<ExecutionResult> {
    const startTime = Date.now();
    const result: ExecutionResult = {
      success: false,
      totalApplicationsChecked: 0,
      remindersEligible: 0,
      remindersSent: 0,
      errors: [],
      executionTime: 0,
    };

    try {
      this.logToFile('info', 'Starting missing document reminder process');
      this.logger.log('Starting missing document reminder process...');

      // Find applications with missing documents
      const applications = await this.findApplicationsWithMissingDocuments();
      result.totalApplicationsChecked = applications.length;
      result.remindersEligible = applications.length;

      this.logger.log(
        `Found ${applications.length} applications eligible for reminders`,
      );

      // Send reminders for each application
      for (const application of applications) {
        try {
          await this.sendReminderEmail(application);
          result.remindersSent++;
          this.logger.log(
            `Reminder sent for application: ${application.application_number}`,
          );
        } catch (error) {
          const errorMsg = `Failed to send reminder for ${application.application_number}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      result.success = result.errors.length === 0;
      result.executionTime = Date.now() - startTime;

      const summary = `Process completed. Sent ${result.remindersSent}/${result.remindersEligible} reminders in ${result.executionTime}ms`;
      this.logger.log(summary);
      this.logToFile('info', summary);

      if (result.errors.length > 0) {
        this.logger.warn(
          `${result.errors.length} errors occurred during execution`,
        );
        this.logToFile(
          'warn',
          `${result.errors.length} errors occurred during execution`,
        );
      }

      return result;
    } catch (error) {
      result.success = false;
      result.executionTime = Date.now() - startTime;
      result.errors.push(`Script execution failed: ${error.message}`);

      this.logger.error(
        `Script execution failed: ${error.message}`,
        error.stack,
      );
      this.logToFile(
        'error',
        `Script execution failed: ${error.message}`,
        'Execution',
        error.stack,
      );

      throw error;
    }
  }

  /**
   * Get scheduler configuration from environment variables
   */
  private getSchedulerConfig(): SchedulerConfig {
    return {
      enabled: process.env.SCHEDULER_ENABLED === 'true',
      hour: parseInt(process.env.SCHEDULER_HOUR || '9', 10),
      minute: parseInt(process.env.SCHEDULER_MINUTE || '0', 10),
      timezone: process.env.SCHEDULER_TIMEZONE || 'UTC',
      checkInterval: parseInt(
        process.env.SCHEDULER_CHECK_INTERVAL || '60000',
        10,
      ),
      logLevel: process.env.SCHEDULER_LOG_LEVEL || 'info',
    };
  }

  /**
   * Start self-contained scheduler
   */
  async startScheduler(): Promise<void> {
    if (this.isSchedulerRunning) {
      this.logger.warn('Scheduler is already running');
      return;
    }

    const config = this.getSchedulerConfig();
    this.isSchedulerRunning = true;

    this.logger.log(
      `Starting scheduler with config: ${JSON.stringify(config)}`,
    );
    this.logToFile(
      'info',
      `Scheduler started with config: ${JSON.stringify(config)}`,
    );

    // Set up graceful shutdown
    process.on('SIGTERM', () => this.stopScheduler());
    process.on('SIGINT', () => this.stopScheduler());

    // Start the scheduling loop
    this.scheduleNextCheck(config);
  }

  /**
   * Schedule next check
   */
  private scheduleNextCheck(config: SchedulerConfig): void {
    if (!this.isSchedulerRunning) return;

    this.schedulerTimeout = setTimeout(async () => {
      if (!this.isSchedulerRunning) return;

      try {
        if (this.isScheduledTime(config)) {
          this.logger.log(
            'Scheduled time reached, executing reminder process...',
          );
          this.logToFile(
            'info',
            'Scheduled time reached, executing reminder process',
          );

          await this.executeOnce();
        }
      } catch (error) {
        this.logger.error(
          `Error during scheduled execution: ${error.message}`,
          error.stack,
        );
        this.logToFile(
          'error',
          `Error during scheduled execution: ${error.message}`,
          'Scheduler',
          error.stack,
        );
      }

      // Schedule next check
      this.scheduleNextCheck(config);
    }, config.checkInterval);
  }

  /**
   * Check if current time matches scheduled time
   */
  private isScheduledTime(config: SchedulerConfig): boolean {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    return currentHour === config.hour && currentMinute === config.minute;
  }

  /**
   * Stop scheduler
   */
  stopScheduler(): void {
    if (!this.isSchedulerRunning) return;

    this.logger.log('Stopping scheduler...');
    this.logToFile('info', 'Scheduler stopping');

    this.isSchedulerRunning = false;

    if (this.schedulerTimeout) {
      clearTimeout(this.schedulerTimeout);
      this.schedulerTimeout = undefined;
    }

    this.logger.log('Scheduler stopped');
    this.logToFile('info', 'Scheduler stopped');
  }

  /**
   * Get scheduler status
   */
  getSchedulerStatus(): { running: boolean; config: SchedulerConfig } {
    return {
      running: this.isSchedulerRunning,
      config: this.getSchedulerConfig(),
    };
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(): {
  mode: 'once' | 'scheduler' | 'status';
  help: boolean;
} {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    return { mode: 'once', help: true };
  }

  if (args.includes('--scheduler') || args.includes('-s')) {
    return { mode: 'scheduler', help: false };
  }

  if (args.includes('--status')) {
    return { mode: 'status', help: false };
  }

  return { mode: 'once', help: false };
}

/**
 * Display help information
 */
function displayHelp(): void {
  console.log(`
Missing Document Reminder System

Usage:
  npm run reminder:documents [options]
  ts-node scripts/document-reminder.ts [options]

Options:
  --scheduler, -s    Run as a long-running scheduler process
  --status          Show scheduler status (if running)
  --help, -h        Show this help message

Environment Variables:
  SCHEDULER_ENABLED=true/false          Enable/disable scheduler
  SCHEDULER_HOUR=9                      Hour to run (0-23)
  SCHEDULER_MINUTE=0                    Minute to run (0-59)
  SCHEDULER_TIMEZONE=UTC                Timezone for scheduling
  SCHEDULER_CHECK_INTERVAL=60000        Check interval in milliseconds
  SCHEDULER_LOG_LEVEL=info              Log level (debug/info/warn/error)

Features:
  - Single self-contained script with all functionality
  - Array operators for database queries (has, hasSome, hasEvery)
  - React email rendering with fallback templates
  - File-based JSON configuration storage
  - Non-blocking email sending
  - File-based error logging
  - 7-day completion timeline in emails
  - Configurable reminder intervals from notification settings

Examples:
  # Run once immediately
  npm run reminder:documents

  # Run as scheduler (long-running process)
  npm run reminder:documents -- --scheduler

  # Run with custom schedule (daily at 2:30 PM)
  SCHEDULER_HOUR=14 SCHEDULER_MINUTE=30 npm run reminder:documents -- --scheduler
`);
}

/**
 * Execute one-time reminder process
 */
async function executeOnce(): Promise<void> {
  let app;

  try {
    // Create NestJS application context
    app = await NestFactory.createApplicationContext(
      ConsolidatedReminderModule,
      {
        logger: ['error', 'warn', 'log'],
      },
    );

    // Get services from DI container
    const prisma = app.get(PrismaService);
    const loggerService = app.get(LoggerService);
    const mailerService = app.get(MailerService);
    const notificationSettings = app.get(NotificationSettingsStorageService);

    // Create and execute service
    const service = new ConsolidatedDocumentReminderService(
      prisma,
      loggerService,
      mailerService,
      notificationSettings,
    );
    const result = await service.executeOnce();

    // Output results
    console.log('\n=== Document Reminder Results ===');
    console.log(`Success: ${result.success}`);
    console.log(`Applications Checked: ${result.totalApplicationsChecked}`);
    console.log(`Reminders Eligible: ${result.remindersEligible}`);
    console.log(`Reminders Sent: ${result.remindersSent}`);
    console.log(`Execution Time: ${result.executionTime}ms`);

    if (result.errors.length > 0) {
      console.log(`Errors: ${result.errors.length}`);
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    process.exit(result.success ? 0 : 1);
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

/**
 * Run scheduler mode
 */
async function runScheduler(): Promise<void> {
  let app;

  try {
    console.log('Starting Consolidated Document Reminder Scheduler...');

    // Create NestJS application context
    app = await NestFactory.createApplicationContext(
      ConsolidatedReminderModule,
      {
        logger: ['error', 'warn', 'log'],
      },
    );

    // Get services from DI container
    const prisma = app.get(PrismaService);
    const loggerService = app.get(LoggerService);
    const mailerService = app.get(MailerService);
    const notificationSettings = app.get(NotificationSettingsStorageService);

    // Create and start scheduler
    const service = new ConsolidatedDocumentReminderService(
      prisma,
      loggerService,
      mailerService,
      notificationSettings,
    );
    await service.startScheduler();

    // Keep the process running
    console.log('Scheduler is running. Press Ctrl+C to stop.');

    // The scheduler handles its own lifecycle and graceful shutdown
    // The process will exit when SIGTERM/SIGINT is received
  } catch (error) {
    console.error('Scheduler failed to start:', error);
    process.exit(1);
  }
}

/**
 * Show scheduler status
 */
async function showStatus(): Promise<void> {
  try {
    console.log('=== Document Reminder Status ===');
    console.log('Status checking is not implemented in this version.');
    console.log('To check if scheduler is running, look for the process:');
    console.log('  ps aux | grep document-reminder');
    console.log('  or check the log files in logs/document-reminders/');
  } catch (error) {
    console.error('Failed to get status:', error);
    process.exit(1);
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  const { mode, help } = parseArguments();

  if (help) {
    displayHelp();
    return;
  }

  switch (mode) {
    case 'once':
      await executeOnce();
      break;
    case 'scheduler':
      await runScheduler();
      break;
    case 'status':
      await showStatus();
      break;
    default:
      console.error('Invalid mode');
      process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}
