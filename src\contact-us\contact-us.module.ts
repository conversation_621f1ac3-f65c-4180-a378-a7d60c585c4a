import { Modu<PERSON> } from '@nestjs/common';
import { ContactUsController } from './contact-us.controller';
import { ContactUsService } from './contact-us.service';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';

@Module({
  controllers: [ContactUsController],
  providers: [ContactUsService, PrismaService, JwtService, MailerService],
})
export class ContactUsModule {}
