# CareerIreland API

<p align="center">
  <img src="https://careerireland.com/_next/image?url=%2Flogo.png&w=256&q=100" width="120" alt="NestJS Logo" />
</p>

<p align="center">
  <a href="#installation">Installation</a> •
  <a href="#api-documentation">API Docs</a> •
  <a href="#developer-guide">Developer Guide</a> •
  <a href="#database-management">Database</a> •
  <a href="#testing">Testing</a>
</p>

---

## 📋 Table of Contents

- [Overview](#overview)
- [Project Setup and Installation](#project-setup-and-installation)
- [API Documentation](#api-documentation)
- [Developer Guide](#developer-guide)
- [Database Management](#database-management)
- [Testing](#testing)
- [Document Reminder System](#document-reminder-system)
- [Production Deployment](#production-deployment)
- [Essential Documentation Links](#essential-documentation-links)

---

## 🌟 Overview

**CareerIreland API** provides comprehensive backend services for a career services platform, offering immigration consultation, training programs, mentorship services, and package management with secure payment processing.

### ✨ Key Features

- **🔐 Authentication & Authorization** - JWT-based multi-role authentication (User, Admin, Agent, Mentor)
- **💳 Payment Processing** - Unified Stripe integration for all service types
- **🏛️ Immigration Services** - Comprehensive immigration consultation management
- **📚 Training Programs** - Course management with progress tracking
- **👨‍🏫 Mentorship Platform** - Mentor-client matching and service delivery
- **📦 Package Management** - Service package creation and ordering
- **📄 Document Management** - Secure document upload, processing, and version control
- **📧 Email Notifications** - Automated email system with React templates
- **📊 Admin Dashboard** - Administrative interface for platform management
- **🔔 Notification System** - Multi-channel notification management
- **🔄 Workflow Engine** - Dynamic workflow management for applications
- **📋 Document Reminder System** - Automated missing document reminder emails

### 🏗️ Technology Stack

- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with refresh tokens
- **Payment**: Stripe integration
- **Storage**: Supabase for file management
- **Email**: React-based email templates with Resend
- **API Documentation**: Swagger/OpenAPI
- **Process Management**: PM2 for production deployment
- **Testing**: Jest with comprehensive test coverage

---

## 🚀 Project Setup and Installation

### Prerequisites

- **Node.js**: Version 18.x or higher
- **PostgreSQL**: Version 13.x or higher
- **npm**: Version 8.x or higher
- **Git**: For version control

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd careerireland-api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   
   Copy the example environment file and configure your settings:
   ```bash
   cp example.env .env
   ```

   **Required Environment Variables:**
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/careerireland"
   
   # JWT Secrets
   JWT_SECRET="your-jwt-secret-key"
   JWT_REFRESH_SECRET="your-refresh-secret-key"
   
   # Stripe Payment
   STRIPE_SECRET_KEY="sk_test_..."
   STRIPE_WEBHOOK_SECRET="whsec_..."
   
   # Email Service (Resend)
   RESEND_API_KEY="re_..."
   
   # Supabase Storage
   SUPABASE_URL="https://your-project.supabase.co"
   SUPABASE_ANON_KEY="your-anon-key"
   SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
   
   # Application
   PORT=4242
   NODE_ENV=development
   WEBSITE="http://localhost:3001"
   ```

### Database Setup

1. **Generate Prisma Client**
   ```bash
   npm run prisma:generate
   ```

2. **Run Database Migrations**
   ```bash
   npm run migrate:dev
   ```

3. **Seed Database (Optional)**
   ```bash
   npm run db:seed
   ```

### Development Server

1. **Start Development Server**
   ```bash
   npm run start:dev
   ```

2. **Verify Installation**
   - API: http://localhost:4242
   - Swagger Documentation: http://localhost:4242/doc
   - Health Check: http://localhost:4242 (should return "Hello World!")

### Production Build

```bash
# Build the application
npm run build

# Start production server
npm run start:prod
```

---

## 📚 API Documentation

### Swagger/OpenAPI Documentation

- **Development**: http://localhost:4242/doc
- **Production**: https://your-domain.com/doc

### Authentication

All authenticated endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Core API Endpoint Groups

#### 🔐 Authentication (`/auth`)
- `GET /auth/profile` - Get current user profile (all user types)

#### 👤 User Management (`/user`)
- `POST /user/register` - User registration
- `POST /user/login` - User login
- `POST /user/refresh` - Refresh JWT token
- `GET /user` - Get user profile (authenticated)
- `PATCH /user` - Update user profile
- `DELETE /user` - Delete user account
- `POST /user/verify` - Email verification

#### 👨‍💼 Admin Management (`/admin`)
- `POST /admin/register` - Admin registration
- `POST /admin/login` - Admin login
- `GET /admin/:id` - Get admin profile
- `GET /user/admin` - Get all users (admin only)
- `POST /user/admin` - Create user (admin only)

#### 🤝 Agent Management (`/agent`)
- `POST /agent/register` - Agent registration
- `POST /agent/login` - Agent login
- `GET /agent/profile` - Get agent profile
- `GET /agent/applications` - Get assigned applications

#### 👨‍🏫 Mentor Management (`/mentor`)
- `POST /mentor/register` - Mentor registration
- `POST /mentor/login` - Mentor login
- `GET /mentor/profile` - Get mentor profile
- `GET /mentor` - List mentors (public)

#### 💳 Payment Processing (`/payment`)
- `POST /payment/mentor-service` - Process mentor service payment
- `POST /payment/guest-service` - Process guest payment
- `POST /payment/package` - Process package payment
- `POST /payment/immigration` - Process immigration service payment
- `POST /payment/training` - Process training payment
- `POST /payment/webhook` - Stripe webhook handler

#### 📄 Document Management (`/documents`)
- `POST /documents/upload` - Upload documents
- `GET /documents` - List documents with filtering
- `GET /documents/:id` - Get document details
- `GET /documents/:id/download` - Download document
- `PUT /documents/:id` - Update document metadata
- `DELETE /documents/:id` - Delete document

#### 📦 Package Management (`/packages`)
- `POST /packages` - Create package (admin only)
- `GET /packages` - List packages
- `GET /packages/:id` - Get package details
- `PUT /packages/:id` - Update package (admin only)
- `DELETE /packages/:id` - Delete package (admin only)

#### 🏛️ Immigration Services (`/immigration`)
- `POST /immigration` - Create immigration service (admin only)
- `GET /immigration` - List immigration services
- `GET /immigration/:id` - Get service details
- `PUT /immigration/:id` - Update service (admin only)

#### 📚 Training Management (`/training`)
- `POST /training` - Create training (admin only)
- `GET /training` - List training programs
- `GET /training/:id` - Get training details
- `PUT /training/:id` - Update training (admin only)

#### 🔔 Notification Settings (`/notifications`)
- `GET /notifications/settings` - Get notification preferences
- `PUT /notifications/settings` - Update notification preferences

#### 📧 OTP Management (`/otp`)
- `POST /otp/generate` - Generate OTP
- `POST /otp/verify` - Verify OTP
- `POST /otp/resend` - Resend OTP

### Example API Calls

#### User Registration
```bash
curl -X POST http://localhost:4242/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "securePassword123"
  }'
```

#### User Login
```bash
curl -X POST http://localhost:4242/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securePassword123"
  }'
```

#### Get User Profile (Authenticated)
```bash
curl -X GET http://localhost:4242/user \
  -H "Authorization: Bearer <your-jwt-token>"
```

---

## 🛠️ Developer Guide

### Code Architecture Overview

The application follows a modular architecture with clear separation of concerns:

```
src/
├── application/          # Core application logic and workflows
├── auth/                # Universal authentication module
├── user/                # User management
├── admin/               # Admin management
├── agent/               # Agent management
├── mentor/              # Mentor management
├── payment/             # Payment processing
├── document/            # Document management
├── packages/            # Package management
├── immigration/         # Immigration services
├── training/            # Training programs
├── mailer/              # Email service
├── utils/               # Utility services
├── guards/              # Authentication guards
├── decorator/           # Custom decorators
└── main.ts              # Application entry point
```

### Database Schema

The application uses PostgreSQL with Prisma ORM. Key entities include:

- **Users** - Multi-role user system (user, admin, agent, mentor)
- **Applications** - Dynamic workflow-based applications
- **Documents** - Document vault with version control
- **Payments** - Unified payment processing
- **Notifications** - Multi-channel notification system
- **Workflows** - Template-based workflow engine

### Development Workflow

1. **Feature Development**
   ```bash
   # Create feature branch
   git checkout -b feature/your-feature-name

   # Make changes and test
   npm run test
   npm run build

   # Commit and push
   git commit -m "feat: add your feature"
   git push origin feature/your-feature-name
   ```

2. **Code Quality**
   ```bash
   # Lint code
   npm run lint

   # Format code
   npm run format

   # Type checking
   npm run build
   ```

### Best Practices

- **Authentication**: Always use appropriate guards (`@UseGuards(JwtGuard)`)
- **Validation**: Use DTOs for request validation
- **Error Handling**: Use NestJS exception filters
- **Logging**: Use the built-in Logger service
- **Testing**: Write unit tests for all services and controllers
- **Documentation**: Update Swagger decorators for API changes

### Debugging

1. **Development Debugging**
   ```bash
   npm run start:debug
   ```

2. **Log Files**
   - Application logs: `logs/`
   - Error logs: `logs/error-*.log`
   - Document reminder logs: `logs/document-reminders/`

3. **Database Debugging**
   ```bash
   # Open Prisma Studio
   npm run prisma:studio
   ```

---

## 🗄️ Database Management (Prisma/PostgreSQL)

### Backup Procedures

#### Creating Database Backups

1. **Manual Backup**
   ```bash
   # Full database backup
   pg_dump -h localhost -U username -d careerireland > backup_$(date +%Y%m%d_%H%M%S).sql

   # Schema-only backup
   pg_dump -h localhost -U username -d careerireland --schema-only > schema_backup.sql

   # Data-only backup
   pg_dump -h localhost -U username -d careerireland --data-only > data_backup.sql
   ```

2. **Automated Backup Script**
   ```bash
   #!/bin/bash
   # backup.sh
   DATE=$(date +%Y%m%d_%H%M%S)
   BACKUP_DIR="/path/to/backups"
   DB_NAME="careerireland"

   pg_dump -h localhost -U username -d $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

   # Keep only last 7 days of backups
   find $BACKUP_DIR -name "backup_*.sql" -mtime +7 -delete
   ```

3. **Scheduled Backups (Cron)**
   ```bash
   # Add to crontab (crontab -e)
   0 2 * * * /path/to/backup.sh
   ```

#### Backup Restoration

```bash
# Restore from backup
psql -h localhost -U username -d careerireland < backup_20250719_120000.sql

# Restore to new database
createdb careerireland_restored
psql -h localhost -U username -d careerireland_restored < backup_20250719_120000.sql
```

### Migration Best Practices

#### Creating Migrations

1. **Schema Changes**
   ```bash
   # Create and apply migration
   npm run migrate:dev

   # Name your migration descriptively
   # Example: "Add user notification preferences"
   ```

2. **Production Migrations**
   ```bash
   # Deploy migrations to production
   npm run prod:build
   ```

#### Rollback Procedures

1. **Rollback Last Migration**
   ```bash
   npm run migrate:reset
   ```

2. **Manual Rollback**
   ```bash
   # Use Prisma migrate resolve for specific issues
   npx prisma migrate resolve --rolled-back <migration-name>
   ```

### Schema Modification Guidelines

#### DO's ✅

- **Always backup before schema changes**
- **Test migrations on development first**
- **Use descriptive migration names**
- **Add indexes for frequently queried fields**
- **Use proper data types and constraints**
- **Document breaking changes in CHANGELOG.md**

#### DON'Ts ❌

- **Don't modify production database directly**
- **Don't delete migration files after deployment**
- **Don't make breaking changes without migration strategy**
- **Don't skip testing migrations**
- **Don't ignore foreign key constraints**

### Database Monitoring

```bash
# Check database connections
SELECT * FROM pg_stat_activity WHERE datname = 'careerireland';

# Check table sizes
SELECT schemaname,tablename,attname,n_distinct,correlation FROM pg_stats;

# Monitor slow queries
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC;
```

---

## 🧪 Testing

### Test Structure

```
test/
├── application/         # Application module tests
├── payment/            # Payment system tests
├── document/           # Document management tests
├── user/               # User management tests
├── agent/              # Agent management tests
├── scripts/            # Script tests
├── fixtures/           # Test data fixtures
└── config/             # Test configuration
```

### Running Tests

#### Unit Tests
```bash
# Run all tests
npm run test

# Run specific module tests
npm run test:payment
npm run test:document
npm run test:user
npm run test:agent

# Run with coverage
npm run test:cov
```

#### Integration Tests
```bash
# End-to-end tests
npm run test:e2e

# Specific integration tests
npm run test:payment:integration
npm run test:document:integration
```

#### Test Scripts
```bash
# Document reminder system tests
npm run test:document-reminder

# Run all test suites
ts-node scripts/run-all-tests.ts
```

### Testing Strategy

1. **Unit Tests** - Test individual services and controllers
2. **Integration Tests** - Test module interactions
3. **E2E Tests** - Test complete user workflows
4. **Script Tests** - Test utility scripts and schedulers

### Test Coverage Requirements

- **Minimum Coverage**: 80%
- **Critical Paths**: 95% (payment, authentication, document processing)
- **New Features**: 100% coverage required

---

## 📋 Document Reminder System

The Document Reminder System is a self-contained service that automatically sends email notifications to users about missing required documents for their applications.

### Quick Start

#### One-time Execution
```bash
npm run reminder:documents
```

#### Scheduler Mode (Long-running Process)
```bash
npm run reminder:documents -- --scheduler
```

#### Status Check
```bash
npm run reminder:documents -- --status
```

### Key Features

- **Automated Reminders**: Sends email notifications for missing documents based on configurable intervals
- **Self-contained Scheduling**: Internal scheduling mechanism (no external cron jobs required)
- **React Email Templates**: Uses existing email template system with fallback HTML templates
- **File-based Configuration**: Uses JSON configuration files for user preferences
- **Comprehensive Logging**: Detailed logging to `logs/document-reminders/` directory
- **Non-blocking Email Sending**: Asynchronous email processing with error recovery

### Configuration

Users can configure their reminder frequency in `config/notification-settings.json`:

```json
{
  "user_id": "user-123",
  "missing_document_reminder_days": 10,
  "agent_assigned": true,
  "case_status_update": true,
  "document_rejection": true
}
```

### Environment Variables

Configure the scheduler using environment variables:

```bash
SCHEDULER_ENABLED=true/false          # Enable/disable scheduler
SCHEDULER_HOUR=9                      # Hour to run (0-23)
SCHEDULER_MINUTE=0                    # Minute to run (0-59)
SCHEDULER_TIMEZONE=UTC                # Timezone for scheduling
SCHEDULER_CHECK_INTERVAL=60000        # Check interval in milliseconds
SCHEDULER_LOG_LEVEL=info              # Log level (debug/info/warn/error)
```

### Testing

```bash
# Run document reminder tests
npm run test:document-reminder

# Run with coverage
npm run test:document-reminder:cov

# Watch mode
npm run test:document-reminder:watch
```

### Production Deployment

```bash
# Build the application
npm run build

# Run as one-time process
npm run reminder:documents

# Run as scheduler (recommended for production)
npm run reminder:documents -- --scheduler
```

For detailed documentation, configuration options, and troubleshooting, see: [Document Reminder System Documentation](scripts/document-reminder.md)

---

## 🚀 Production Deployment

### PM2 Process Management

1. **Start Application**
   ```bash
   # Start with PM2
   pm2 start ecosystem.config.js

   # Check status
   pm2 status

   # View logs
   pm2 logs
   ```

2. **PM2 Configuration** (`ecosystem.config.js`)
   ```javascript
   module.exports = {
     apps: [
       {
         name: 'careerireland-api',
         script: 'dist/src/main.js',
         instances: 1,
         autorestart: true,
         max_memory_restart: '1G',
         env: {
           NODE_ENV: 'production',
           PORT: 4242,
         },
       },
       {
         name: 'careerireland-scheduler',
         script: 'dist/scripts/document-reminder.js',
         args: '--scheduler',
         instances: 1,
         autorestart: true,
         max_memory_restart: '512M',
       },
     ],
   };
   ```

### Environment Configuration

1. **Production Environment Variables**
   ```env
   NODE_ENV=production
   PORT=4242
   DATABASE_URL="**********************************************/careerireland"
   JWT_SECRET="production-jwt-secret"
   STRIPE_SECRET_KEY="sk_live_..."
   ```

2. **SSL/HTTPS Configuration**
   - Use reverse proxy (Nginx) for SSL termination
   - Configure CORS for production domains
   - Set secure cookie flags

### Monitoring and Logging

1. **Application Logs**
   ```bash
   # View application logs
   tail -f logs/api-combined.log

   # View error logs
   tail -f logs/api-error.log
   ```

2. **Health Checks**
   ```bash
   # API health check
   curl http://localhost:4242

   # Database connectivity
   curl http://localhost:4242/health/db
   ```

---

## 📖 Essential Documentation Links

### Project Documentation
- [API Documentation](docs/API_DOCUMENTATION.md) - Comprehensive API reference
- [Payment Integration Guide](docs/README_PAYMENT_INTEGRATION.md) - Payment system documentation
- [Frontend Integration Guide](docs/FRONTEND_INTEGRATION_GUIDE.md) - Frontend integration instructions
- [Notification Configuration](docs/NOTIFICATION-CONFIGURATION.md) - Notification system setup

### Development Resources
- [Test Commands](docs/TEST_COMMANDS.md) - Testing guide and commands
- [Project Development Protocol](docs/PDP.md) - Development workflow and standards
- [Product Requirements](docs/PRD.md) - Product requirements document
- [Document Reminder System](scripts/document-reminder.md) - Missing document reminder system documentation

### Architecture Documentation
- [Application Module README](src/application/README.md) - Core application architecture
- [Document Management README](src/document/README.md) - Document system architecture
- [Template System README](src/template/README.md) - Email template system

### External Resources
- [NestJS Documentation](https://docs.nestjs.com/) - Framework documentation
- [Prisma Documentation](https://www.prisma.io/docs/) - Database ORM documentation
- [Stripe API Documentation](https://stripe.com/docs/api) - Payment processing
- [Swagger/OpenAPI](https://swagger.io/docs/) - API documentation standard

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the UNLICENSED License.

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.

---

<p align="center">
  <strong>Built with ❤️ by the CareerIreland Development Team</strong>
</p>
