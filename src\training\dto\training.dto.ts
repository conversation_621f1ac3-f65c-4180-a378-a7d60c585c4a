import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsInt,
  IsString,
  Min,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';

export class TrainingDto {
  @ApiProperty({ description: 'Name of the training' })
  @IsString()
  @IsNotEmpty()
  name: string;
  @ApiProperty({ description: 'Image of the training' })
  @IsString()
  @IsNotEmpty()
  img: string;

  @ApiProperty({ description: 'Amount of the training in integer format' })
  @IsInt()
  @Min(0)
  amount: number;

  @ApiProperty({ description: 'Training order' })
  @IsOptional()
  @IsInt()
  @Min(0)
  order: number;

  @IsArray()
  @IsString({ each: true })
  service: string[];

  @IsArray()
  @IsString({ each: true })
  highlights: string[];
}
