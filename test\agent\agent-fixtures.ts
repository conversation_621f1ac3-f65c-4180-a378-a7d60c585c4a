/**
 * Agent Test Fixtures
 *
 * Reusable test data and mock objects for agent testing.
 */

import { AgentStatus, ApplicationStatus, PriorityLevel } from '@prisma/client';

export const mockAdminUser = {
  id: 'admin_123456789',
  name: 'Admin User',
  email: '<EMAIL>',
  emailVerified: true,
  image: null,
  password: 'hashed_admin_password',
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
};

export const mockAgentUser = {
  id: 'agent_123456789',
  name: '<PERSON>',
  email: '<EMAIL>',
  password_hash: 'hashed_agent_password',
  phone: '+353-1-234-5678',
  status: AgentStatus.Active,
  created_at: new Date('2024-01-01T00:00:00Z'),
  updated_at: new Date('2024-01-01T00:00:00Z'),
  created_by_admin_id: mockAdminUser.id,
};

export const mockAgentWithAdmin = {
  ...mockAgentUser,
  created_by_admin: {
    id: mockAdminUser.id,
    name: mockAdminUser.name,
    email: mockAdminUser.email,
  },
};

export const mockInactiveAgent = {
  ...mockAgentUser,
  id: 'agent_inactive_123',
  email: '<EMAIL>',
  status: AgentStatus.Inactive,
};

export const mockSuspendedAgent = {
  ...mockAgentUser,
  id: 'agent_suspended_123',
  email: '<EMAIL>',
  status: AgentStatus.Suspended,
};

export const mockApplication = {
  id: 'app_123456789',
  application_number: 'IMM-2024-000001',
  service_type: 'immigration',
  status: ApplicationStatus.Draft,
  priority_level: PriorityLevel.Medium,
  assigned_to: null,
  user_id: 'user_123456789',
  guest_name: null,
  guest_email: null,
  guest_mobile: null,
  payment_id: 'payment_123456789',
  workflow_template_id: 'template_123456789',
  created_at: new Date('2024-01-01T00:00:00Z'),
  updated_at: new Date('2024-01-01T00:00:00Z'),
};

export const mockAssignedApplication = {
  ...mockApplication,
  id: 'app_assigned_123',
  application_number: 'IMM-2024-000002',
  assigned_to: mockAgentUser.id,
  status: ApplicationStatus.Under_Review,
};

export const mockCreateAgentDto = {
  name: 'Jane Doe',
  email: '<EMAIL>',
  phone: '+353-1-987-6543',
  status: AgentStatus.Active,
};

export const mockAgentLoginDto = {
  email: '<EMAIL>',
  password: 'password123',
};

export const mockUpdateAgentPasswordDto = {
  currentPassword: 'oldPassword123',
  newPassword: 'newSecurePassword456',
};

export const mockResetAgentPasswordDto = {
  email: '<EMAIL>',
};

export const mockConfirmResetPasswordDto = {
  token: 'valid_reset_token_123',
  newPassword: 'newResetPassword789',
};

export const mockUpdateAgentDto = {
  name: 'John Smith Updated',
  email: '<EMAIL>',
  phone: '+353-1-555-0123',
};

export const mockAgentQueryDto = {
  page: 1,
  limit: 10,
  status: AgentStatus.Active,
  search: 'john',
};

export const mockUpdatePriorityDto = {
  priority_level: 'High' as const,
};

export const mockAssignApplicationDto = {
  agentId: mockAgentUser.id,
};

export const mockJwtPayload = {
  id: mockAgentUser.id,
  email: mockAgentUser.email,
  tokenType: 'agent',
  sub: {
    name: mockAgentUser.name,
  },
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
};

export const mockAdminJwtPayload = {
  id: mockAdminUser.id,
  email: mockAdminUser.email,
  tokenType: 'admin',
  sub: {
    name: mockAdminUser.name,
  },
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
};

export const mockAuthTokens = {
  accessToken: 'mock_access_token_123',
  refreshToken: 'mock_refresh_token_123',
  expiresIn: Date.now() + 5 * 60 * 60 * 1000, // 5 hours
};

export const mockAgentCreationResponse = {
  success: true,
  message: 'Agent created successfully. Welcome email sent.',
  agent: {
    id: mockAgentUser.id,
    name: mockAgentUser.name,
    email: mockAgentUser.email,
    phone: mockAgentUser.phone,
    status: mockAgentUser.status,
    created_at: mockAgentUser.created_at,
    updated_at: mockAgentUser.updated_at,
  },
  temporaryPassword: 'TEMP12345678',
};

export const mockAgentListResponse = {
  agents: [mockAgentWithAdmin],
  total: 1,
  page: 1,
  limit: 10,
  totalPages: 1,
};

export const mockPriorityUpdateResponse = {
  success: true,
  message: 'Application priority updated successfully',
  priority_level: 'High',
  applicationId: mockApplication.id,
};

export const mockAssignmentResponse = {
  success: true,
  message: 'Application assigned successfully',
  assignedAgent: {
    id: mockAgentUser.id,
    name: mockAgentUser.name,
    email: mockAgentUser.email,
  },
  applicationId: mockApplication.id,
};

// Helper functions for creating test data
export const createMockAgent = (
  overrides: Partial<typeof mockAgentUser> = {},
) => ({
  ...mockAgentUser,
  ...overrides,
});

export const createMockApplication = (
  overrides: Partial<typeof mockApplication> = {},
) => ({
  ...mockApplication,
  ...overrides,
});

export const createMockJwtPayload = (
  overrides: Partial<typeof mockJwtPayload> = {},
) => ({
  ...mockJwtPayload,
  ...overrides,
});

// Mock service responses
export const mockPrismaServiceMethods = {
  agent: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  admin: {
    findUnique: jest.fn(),
  },
  application: {
    findUnique: jest.fn(),
    count: jest.fn(),
    update: jest.fn(),
  },
};

export const mockMailerServiceMethods = {
  sendEmail: jest.fn(),
};

export const mockLoggerServiceMethods = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

export const mockJwtServiceMethods = {
  signAsync: jest.fn(),
  verifyAsync: jest.fn(),
};

// Reset all mocks helper
export const resetAllMocks = () => {
  Object.values(mockPrismaServiceMethods.agent).forEach((mock) =>
    mock.mockReset(),
  );
  Object.values(mockPrismaServiceMethods.admin).forEach((mock) =>
    mock.mockReset(),
  );
  Object.values(mockPrismaServiceMethods.application).forEach((mock) =>
    mock.mockReset(),
  );
  Object.values(mockMailerServiceMethods).forEach((mock) => mock.mockReset());
  Object.values(mockLoggerServiceMethods).forEach((mock) => mock.mockReset());
  Object.values(mockJwtServiceMethods).forEach((mock) => mock.mockReset());
};
