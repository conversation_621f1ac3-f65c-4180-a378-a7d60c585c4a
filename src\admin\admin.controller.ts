import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtGuard } from 'src/guards/jwt.guard';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RefreshJwtGuard } from 'src/guards/refresh.guard';
import { CreateUserDto, LoginDto, ProgressDto } from './dto/admin.dto';
import { AdminService } from './admin.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';

@ApiTags('admin')
@Controller('admin')
export class AdminController {
  constructor(private admin: AdminService) {}

  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @Get(':id')
  async getUserProfile(@Param('id') id: string) {
    return await this.admin.findById(id);
  }

  @Post('register')
  async registerUser(@Body() dto: CreateUserDto) {
    return await this.admin.create(dto);
  }

  @Post('login')
  async login(@Body() dto: LoginDto) {
    return await this.admin.login(dto);
  }

  @UseGuards(RefreshJwtGuard)
  @Post('refresh')
  async refreshToken(@Request() req) {
    return await this.admin.refreshToken(req.user);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/service')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_mentor_service(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_mentor_service(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-service')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_mentor_service(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_mentor_service(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/package')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_package(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_package(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-package')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_package(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_package(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/immigration')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_immigration(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_immigration(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-immigration')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_immigration(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_immigration(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/training')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async progress_update_training(@Body() dto: ProgressDto) {
    return await this.admin.progress_update_training(dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/user/progress/guest-training')
  @ApiOperation({
    summary: '(Admin only) | Update Progress',
    description:
      'This API is restricted to admin  and requires a Bearer token for authentication.',
  })
  async guest_progress_update_training(@Body() dto: ProgressDto) {
    return await this.admin.guest_progress_update_training(dto);
  }
}
