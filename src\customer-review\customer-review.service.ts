import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { CustomerReviewDto } from './dto/customer-review.dto';

@Injectable()
export class CustomerReviewService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CustomerReviewDto) {
    const data = await this.prisma.customer_review.create({
      data: dto,
    });

    return data;
  }
  async update(id: string, dto: CustomerReviewDto) {
    const data = await this.prisma.customer_review.update({
      where: {
        id,
      },
      data: dto,
    });

    return data;
  }
  async remove(id: string) {
    const data = await this.prisma.customer_review.delete({
      where: {
        id,
      },
    });

    return data;
  }
  async getAll(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;
    const data = await this.prisma.customer_review.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      orderBy: [{ order: 'asc' }, { date: 'desc' }],
    });

    return data;
  }
  async get(id: string) {
    const data = await this.prisma.customer_review.findUnique({
      where: {
        id,
      },
    });

    return data;
  }
}
