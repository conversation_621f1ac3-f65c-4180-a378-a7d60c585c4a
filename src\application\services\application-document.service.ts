/**
 * Application Document Service
 *
 * Service for managing application document requirements from workflow templates.
 * This service populates document placeholders when applications are created from payments,
 * similar to how ApplicationFormService handles form fields.
 *
 * Key Features:
 * - Populate document requirements from workflow templates
 * - Create document placeholders in application_document table
 * - Maintain consistency with existing document management patterns
 * - Comprehensive error handling and logging
 */

import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../utils/prisma.service';

@Injectable()
export class ApplicationDocumentService {
  private readonly logger = new Logger(ApplicationDocumentService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Populate document requirements from workflow template
   * Called when an application is created to initialize document placeholders
   */
  async populateDocumentRequirementsFromTemplate(
    applicationId: string,
    workflowTemplate: any[],
  ): Promise<void> {
    try {
      this.logger.log(
        `Populating document requirements for application: ${applicationId}`,
      );

      if (!workflowTemplate || !Array.isArray(workflowTemplate)) {
        this.logger.warn(
          `No valid workflow template found for application: ${applicationId}`,
        );
        return;
      }

      const documentRecords = [];

      // Process each stage in the workflow template
      for (const stage of workflowTemplate) {
        const stageOrder = stage.stageOrder || 1;

        // Check if stage has document requirements
        if (
          stage.documentsRequired &&
          stage.documents &&
          Array.isArray(stage.documents)
        ) {
          for (const document of stage.documents) {
            // Create document placeholder record
            documentRecords.push({
              id: `ad_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              application_id: applicationId,
              document_vault_id: '', // Will be populated when document is uploaded
              stage_order: stageOrder,
              file_name: document.documentName || 'Unknown Document',
              file_url: '', // Will be populated when document is uploaded
              required: document.required || false,
              status: 'pending', // Default status for new requirements
              request_reason: document.documentName || 'Required Document',
              uploaded_by: null, // Will be set when document is uploaded
              upload_date: new Date(),
            });
          }
        }
      }

      if (documentRecords.length > 0) {
        // Note: We cannot use createMany with empty document_vault_id due to foreign key constraints
        // Instead, we'll create records one by one with proper handling
        let createdCount = 0;

        for (const record of documentRecords) {
          try {
            // Create a temporary document vault entry for the placeholder
            const tempDocument = await this.prisma.document_vault.create({
              data: {
                document_name: record.file_name,
                original_filename: record.file_name,
                document_type: 'Other', // Use valid enum value for placeholders
                document_category: 'required',
                file_path: '', // Empty until actual file is uploaded
                file_size: 0,
                // Note: file_hash field removed from schema
                // Note: mime_type field removed from schema
                // Note: status field removed from schema
                // Note: version field removed from schema
                // Note: is_current_version field removed from schema
                user_id: null, // Will be set when document is uploaded
                guest_email: null,
                // Note: tags field removed from schema
                uploaded_by: null,
              },
            });

            // Now create the application_document record with the proper document_vault_id
            await this.prisma.application_document.create({
              data: {
                application_id: record.application_id,
                document_vault_id: tempDocument.id,
                stage_order: record.stage_order,
                file_name: record.file_name,
                file_url: record.file_url,
                required: record.required,
                status: record.status,
                request_reason: record.request_reason,
                uploaded_by: record.uploaded_by,
                upload_date: record.upload_date,
              },
            });

            createdCount++;
          } catch (recordError) {
            this.logger.warn(
              `Failed to create document requirement: ${record.file_name} for application: ${applicationId}`,
              recordError,
            );
            // Continue with other records even if one fails
          }
        }

        this.logger.log(
          `Created ${createdCount} document requirements for application: ${applicationId}`,
        );
      } else {
        this.logger.log(
          `No document requirements to create for application: ${applicationId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to populate document requirements for application: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get document requirements for an application
   */
  async getDocumentRequirements(applicationId: string): Promise<any[]> {
    try {
      const documentRequirements =
        await this.prisma.application_document.findMany({
          where: { application_id: applicationId },
          include: {
            document: {
              select: {
                id: true,
                document_name: true,
                original_filename: true,
                file_path: true,
                // Note: status field removed from schema
                created_at: true,
              },
            },
          },
          orderBy: [{ stage_order: 'asc' }, { file_name: 'asc' }],
        });

      return documentRequirements;
    } catch (error) {
      this.logger.error(
        `Failed to get document requirements for application: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update document requirement status
   */
  async updateDocumentRequirementStatus(
    applicationId: string,
    documentId: string,
    status: string,
    reviewComments?: string,
  ): Promise<void> {
    try {
      await this.prisma.application_document.updateMany({
        where: {
          application_id: applicationId,
          document_vault_id: documentId,
        },
        data: {
          status,
          review_comments: reviewComments,
          reviewed_at: new Date(),
        },
      });

      this.logger.log(
        `Updated document requirement status: ${status} for application: ${applicationId}, document: ${documentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update document requirement status for application: ${applicationId}`,
        error,
      );
      throw error;
    }
  }
}
