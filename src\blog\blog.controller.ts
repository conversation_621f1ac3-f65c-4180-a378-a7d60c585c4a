import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { BlogService } from './blog.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { BlogDto } from './dto/blog.dto';

@ApiTags('blog')
@Controller('blog')
export class BlogController {
  constructor(private blog: BlogService) {}

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async create(@Body() dto: BlogDto) {
    return await this.blog.create(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/:blogId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async update(@Param('blogId') id: string, @Body() dto: BlogDto) {
    return await this.blog.update(id, dto);
  }
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/:blogId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async remove(@Param('blogId') id: string) {
    return await this.blog.remove(id);
  }

  @Get('')
  async getBlogs(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.blog.getBlogs(page, limit);
  }

  @Get('/:slug')
  async getBlog(@Param('slug') slug: string) {
    return await this.blog.getBlog(slug);
  }
}
