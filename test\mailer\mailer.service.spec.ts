/**
 * Mailer Service Tests
 *
 * Comprehensive test suite for MailerService.
 * Tests email sending functionality using Resend API.
 *
 * @desc Tests the email service for sending emails via Resend
 * @assumptions Uses mocked Resend API for email testing
 * @mocked_dependencies Resend
 */

import { Test, TestingModule } from '@nestjs/testing';
import { MailerService } from '../../src/mailer/mailer.service';
import { ResendEmailDto } from '../../src/mailer/dto/mailer.dto';

// Mock Resend
const mockResendSend = jest.fn();
const mockResend = {
  emails: {
    send: mockResendSend,
  },
};

jest.mock('resend', () => ({
  Resend: jest.fn().mockImplementation(() => mockResend),
}));

describe('MailerService', () => {
  let service: MailerService;
  let consoleSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MailerService],
    }).compile();

    service = module.get<MailerService>(MailerService);
    
    // Mock console.error
    consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    
    // Reset mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
    consoleSpy.mockRestore();
  });

  describe('Service Initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });
  });

  describe('sendEmail', () => {
    const validEmailDto: ResendEmailDto = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: 'Test Subject',
      html: '<h1>Test Email</h1>',
    };

    describe('Success Cases', () => {
      it('should send email successfully', async () => {
        // Arrange
        const mockResponse = {
          data: { id: 'email_123', status: 'sent' },
          error: null,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(validEmailDto);

        // Assert
        expect(mockResendSend).toHaveBeenCalledWith({
          from: validEmailDto.from,
          to: [validEmailDto.to],
          subject: validEmailDto.subject,
          html: validEmailDto.html,
        });
        expect(result).toEqual(mockResponse.data);
      });

      it('should handle multiple recipients correctly', async () => {
        // Arrange
        const emailDto = { ...validEmailDto, to: '<EMAIL>' };
        const mockResponse = {
          data: { id: 'email_456', status: 'sent' },
          error: null,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(emailDto);

        // Assert
        expect(mockResendSend).toHaveBeenCalledWith({
          from: emailDto.from,
          to: ['<EMAIL>'],
          subject: emailDto.subject,
          html: emailDto.html,
        });
        expect(result).toEqual(mockResponse.data);
      });

      it('should send email with complex HTML content', async () => {
        // Arrange
        const complexHtml = `
          <html>
            <body>
              <h1>Welcome!</h1>
              <p>This is a <strong>test</strong> email.</p>
              <a href="https://example.com">Click here</a>
            </body>
          </html>
        `;
        const emailDto = { ...validEmailDto, html: complexHtml };
        const mockResponse = {
          data: { id: 'email_789', status: 'sent' },
          error: null,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(emailDto);

        // Assert
        expect(mockResendSend).toHaveBeenCalledWith({
          from: emailDto.from,
          to: [emailDto.to],
          subject: emailDto.subject,
          html: complexHtml,
        });
        expect(result).toEqual(mockResponse.data);
      });
    });

    describe('Error Cases', () => {
      it('should log error when Resend API returns error', async () => {
        // Arrange
        const apiError = { message: 'Invalid API key' };
        const mockResponse = {
          data: null,
          error: apiError,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(validEmailDto);

        // Assert
        expect(consoleSpy).toHaveBeenCalledWith(apiError);
        expect(result).toBeNull();
      });

      it('should handle network errors', async () => {
        // Arrange
        const networkError = new Error('Network error');
        mockResendSend.mockRejectedValue(networkError);

        // Act & Assert
        await expect(service.sendEmail(validEmailDto)).rejects.toThrow(
          'Network error',
        );
      });

      it('should handle authentication errors', async () => {
        // Arrange
        const authError = { message: 'Unauthorized' };
        const mockResponse = {
          data: null,
          error: authError,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(validEmailDto);

        // Assert
        expect(consoleSpy).toHaveBeenCalledWith(authError);
        expect(result).toBeNull();
      });

      it('should handle rate limiting errors', async () => {
        // Arrange
        const rateLimitError = { message: 'Rate limit exceeded' };
        const mockResponse = {
          data: null,
          error: rateLimitError,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(validEmailDto);

        // Assert
        expect(consoleSpy).toHaveBeenCalledWith(rateLimitError);
        expect(result).toBeNull();
      });
    });

    describe('Edge Cases', () => {
      it('should handle empty subject', async () => {
        // Arrange
        const emailDto = { ...validEmailDto, subject: '' };
        const mockResponse = {
          data: { id: 'email_empty_subject', status: 'sent' },
          error: null,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(emailDto);

        // Assert
        expect(mockResendSend).toHaveBeenCalledWith({
          from: emailDto.from,
          to: [emailDto.to],
          subject: '',
          html: emailDto.html,
        });
        expect(result).toEqual(mockResponse.data);
      });

      it('should handle empty HTML content', async () => {
        // Arrange
        const emailDto = { ...validEmailDto, html: '' };
        const mockResponse = {
          data: { id: 'email_empty_html', status: 'sent' },
          error: null,
        };
        mockResendSend.mockResolvedValue(mockResponse);

        // Act
        const result = await service.sendEmail(emailDto);

        // Assert
        expect(mockResendSend).toHaveBeenCalledWith({
          from: emailDto.from,
          to: [emailDto.to],
          subject: emailDto.subject,
          html: '',
        });
        expect(result).toEqual(mockResponse.data);
      });
    });
  });
});
