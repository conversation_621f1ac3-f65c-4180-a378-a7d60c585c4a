import { IsBoolean } from '@nestjs/class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString, Matches } from 'class-validator';

export class CreateUserDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({ required: false, description: 'Mobile number of the user' })
  @IsOptional()
  @IsString()
  @Matches(/^[\+]?[1-9][\d]{0,14}$/, {
    message: 'Mobile number must be a valid phone number format',
  })
  mobileNo?: string;
}

export class LoginDto {
  @ApiProperty()
  @IsEmail()
  email: string;
  @ApiProperty()
  @IsString()
  password: string;
}
export class UpdateUserDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsEmail()
  @IsOptional()
  email: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  password: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  image: string;

  @ApiProperty({ required: false, description: 'Mobile number of the user' })
  @IsOptional()
  @IsString()
  @Matches(/^[\+]?[1-9][\d]{0,14}$/, {
    message: 'Mobile number must be a valid phone number format',
  })
  mobile_no?: string;
}
