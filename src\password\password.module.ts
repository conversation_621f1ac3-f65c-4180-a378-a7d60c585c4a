import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { PasswordController } from './password.controller';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from 'src/mailer/mailer.service';
import { PasswordService } from './password.service';

@Module({
  controllers: [PasswordController],
  providers: [PrismaService, JwtService, MailerService, PasswordService],
})
export class PasswordModule {}
