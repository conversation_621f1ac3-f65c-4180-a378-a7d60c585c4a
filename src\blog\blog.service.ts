import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { BlogDto } from './dto/blog.dto';

@Injectable()
export class BlogService {
  constructor(private prisma: PrismaService) {}

  async create(dto: BlogDto) {
    const blog = await this.prisma.blog.create({
      data: {
        ...dto,
        slug: dto.title.toLowerCase().replaceAll(' ', '-'),
      },
    });

    return blog;
  }
  async update(id: string, dto: BlogDto) {
    const blog = await this.prisma.blog.update({
      where: {
        id,
      },
      data: {
        ...dto,
        slug: dto.title.toLowerCase().replaceAll(' ', '-'),
      },
    });

    return blog;
  }
  async remove(id: string) {
    const blog = await this.prisma.blog.delete({
      where: {
        id,
      },
    });

    return blog;
  }
  async getBlogs(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;

    const blogs = await this.prisma.blog.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return blogs;
  }
  async getBlog(slug: string) {
    const blog = await this.prisma.blog.findUnique({
      where: {
        slug,
      },
    });

    return blog;
  }
}
