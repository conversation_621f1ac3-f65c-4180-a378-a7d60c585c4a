/**
 * Application Status Tests
 *
 * Comprehensive tests for application status logic ensuring that:
 * - Applications created via payment webhook have status "Pending"
 * - Applications created via POST /applications have status "Pending"
 * - Other creation methods maintain existing behavior
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-10
 */

import { Test, TestingModule } from '@nestjs/testing';
import {
  ApplicationService,
  CreateApplicationData,
} from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { ApplicationFormService } from '../../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { ApplicationStatus, PriorityLevel } from '@prisma/client';
import { CreateNewApplicationDto } from '../../src/application/dto/application.dto';

describe('ApplicationService - Status Logic', () => {
  let service: ApplicationService;
  let prismaService: PrismaService;
  let loggerService: LoggerService;

  const mockPrismaService = {
    application: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    payment: {
      findMany: jest.fn(),
    },
    agent: {
      findMany: jest.fn(),
    },
    workflow_template: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  // Mock other required services
  const mockApplicationFormService = {
    updateStageFields: jest.fn(),
  };

  const mockApplicationDocumentService = {
    createDocuments: jest.fn(),
  };

  const mockApplicationTransformerService = {
    transformApplicationDetails: jest.fn(),
  };

  const mockMediaService = {
    uploadFile: jest.fn(),
  };

  const mockDocumentVaultService = {
    storeDocument: jest.fn(),
  };

  const mockNotificationService = {
    sendNotification: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: LoggerService, useValue: mockLoggerService },
        {
          provide: ApplicationFormService,
          useValue: mockApplicationFormService,
        },
        {
          provide: ApplicationDocumentService,
          useValue: mockApplicationDocumentService,
        },
        {
          provide: ApplicationTransformerService,
          useValue: mockApplicationTransformerService,
        },
        { provide: MediaService, useValue: mockMediaService },
        { provide: DocumentVaultService, useValue: mockDocumentVaultService },
        { provide: NotificationService, useValue: mockNotificationService },
      ],
    }).compile();

    service = module.get<ApplicationService>(ApplicationService);
    prismaService = module.get<PrismaService>(PrismaService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createApplicationFromPayment', () => {
    const mockPaymentData: CreateApplicationData = {
      paymentId: 'payment_123',
      serviceType: 'immigration',
      serviceId: 'service_123',
      workflowTemplateId: 'template_123',
      userId: 'user_123',
    };

    it('should create application with Pending status for payment webhook', async () => {
      const mockApplication = {
        id: 'app_123',
        application_number: 'IMM-2025-000001',
        service_type: 'immigration',
        status: ApplicationStatus.Pending,
        priority_level: PriorityLevel.Medium,
        user_id: 'user_123',
        payment_ids: ['payment_123'],
        agent_ids: [],
        current_step: '1',
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock no existing application
      (mockPrismaService.application.findFirst as jest.Mock).mockResolvedValue(
        null,
      );
      (mockPrismaService.application.count as jest.Mock).mockResolvedValue(0);
      (mockPrismaService.application.create as jest.Mock).mockResolvedValue(
        mockApplication,
      );

      const result =
        await service.createApplicationFromPayment(mockPaymentData);

      expect(result).toEqual(mockApplication);
      expect(mockPrismaService.application.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: ApplicationStatus.Pending,
          payment_ids: ['payment_123'],
          user_id: 'user_123',
        }),
      });
    });

    it('should create application with Pending status for guest payment webhook', async () => {
      const guestPaymentData: CreateApplicationData = {
        paymentId: 'payment_guest',
        serviceType: 'immigration',
        serviceId: 'service_123',
        workflowTemplateId: 'template_123',
        guestName: 'John Doe',
        guestEmail: '<EMAIL>',
        guestMobile: '+1234567890',
      };

      const mockApplication = {
        id: 'app_guest',
        application_number: 'IMM-2025-000002',
        service_type: 'immigration',
        status: ApplicationStatus.Pending,
        guest_name: 'John Doe',
        guest_email: '<EMAIL>',
        guest_mobile: '+1234567890',
        payment_ids: ['payment_guest'],
        agent_ids: [],
        current_step: '1',
      };

      (mockPrismaService.application.findFirst as jest.Mock).mockResolvedValue(
        null,
      );
      (mockPrismaService.application.count as jest.Mock).mockResolvedValue(1);
      (mockPrismaService.application.create as jest.Mock).mockResolvedValue(
        mockApplication,
      );

      const result =
        await service.createApplicationFromPayment(guestPaymentData);

      expect(result).toEqual(mockApplication);
      expect(mockPrismaService.application.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: ApplicationStatus.Pending,
          guest_name: 'John Doe',
          guest_email: '<EMAIL>',
          guest_mobile: '+1234567890',
        }),
      });
    });

    it('should return existing application if payment already processed', async () => {
      const existingApplication = {
        id: 'app_existing',
        application_number: 'IMM-2025-000001',
        payment_ids: ['payment_123'],
        status: ApplicationStatus.Pending,
      };

      (mockPrismaService.application.findFirst as jest.Mock).mockResolvedValue(
        existingApplication,
      );

      const result =
        await service.createApplicationFromPayment(mockPaymentData);

      expect(result).toEqual(existingApplication);
      expect(mockPrismaService.application.create).not.toHaveBeenCalled();
    });
  });

  describe('createNewApplication', () => {
    const mockCreateDto: CreateNewApplicationDto = {
      service_type: 'immigration',
      service_id: 'service_123',
      user_id: 'user_123',
      workflow_template_id: 'template_123',
      priority_level: PriorityLevel.Medium,
      payments: ['payment_1', 'payment_2'],
      assigned_agent: ['agent_1', 'agent_2'],
    };

    it('should create application with Pending status for POST /applications endpoint', async () => {
      const mockApplication = {
        id: 'app_new',
        application_number: 'IMM-2025-000003',
        service_type: 'immigration',
        status: ApplicationStatus.Pending,
        priority_level: PriorityLevel.Medium,
        user_id: 'user_123',
        payment_ids: ['payment_1', 'payment_2'],
        agent_ids: ['agent_1', 'agent_2'],
        current_step: '1',
        created_at: new Date(),
        updated_at: new Date(),
        user: { id: 'user_123', name: 'John Doe', email: '<EMAIL>' },
        workflow_template: {
          id: 'template_123',
          name: 'Immigration Template',
          description: 'Standard immigration workflow',
          workflowTemplate: {},
        },
      };

      // Mock validations
      (mockPrismaService.payment.findMany as jest.Mock).mockResolvedValue([
        { id: 'payment_1' },
        { id: 'payment_2' },
      ]);
      (mockPrismaService.agent.findMany as jest.Mock).mockResolvedValue([
        { id: 'agent_1' },
        { id: 'agent_2' },
      ]);
      (
        mockPrismaService.workflow_template.findUnique as jest.Mock
      ).mockResolvedValue({
        id: 'template_123',
      });

      // Mock application creation
      (mockPrismaService.application.count as jest.Mock).mockResolvedValue(2);
      (mockPrismaService.application.create as jest.Mock).mockResolvedValue(
        mockApplication,
      );

      const result = await service.createNewApplication(
        mockCreateDto,
        'admin_123',
      );

      expect(result).toBeDefined();
      expect(mockPrismaService.application.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: ApplicationStatus.Pending,
          payment_ids: ['payment_1', 'payment_2'],
          agent_ids: ['agent_1', 'agent_2'],
          created_by: 'admin_123',
        }),
        include: expect.any(Object),
      });
    });

    it('should validate payment IDs before creating application', async () => {
      // Mock invalid payment IDs
      (mockPrismaService.payment.findMany as jest.Mock).mockResolvedValue([
        { id: 'payment_1' }, // Only one payment found, but two requested
      ]);

      await expect(
        service.createNewApplication(mockCreateDto, 'admin_123'),
      ).rejects.toThrow('One or more payment IDs are invalid');

      expect(mockPrismaService.application.create).not.toHaveBeenCalled();
    });

    it('should handle optional agent assignment in application creation', async () => {
      // Mock valid payments and agents
      (mockPrismaService.payment.findMany as jest.Mock).mockResolvedValue([
        { id: 'payment_1' },
        { id: 'payment_2' },
      ]);
      (
        mockPrismaService.workflow_template.findUnique as jest.Mock
      ).mockResolvedValue({
        id: 'template_123',
      });
      (mockPrismaService.application.count as jest.Mock).mockResolvedValue(3);

      const mockApplication = {
        id: 'app_agents',
        application_number: 'IMM-2025-000004',
        status: ApplicationStatus.Pending,
        agent_ids: ['agent_1', 'agent_2'],
      };
      (mockPrismaService.application.create as jest.Mock).mockResolvedValue(
        mockApplication,
      );

      const result = await service.createNewApplication(
        mockCreateDto,
        'admin_123',
      );

      expect(result).toBeDefined();
      expect(mockPrismaService.application.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          status: ApplicationStatus.Pending,
          agent_ids: ['agent_1', 'agent_2'],
        }),
        include: expect.any(Object),
      });
    });

    it('should validate workflow template before creating application', async () => {
      // Mock valid payments and agents but invalid workflow template
      (mockPrismaService.payment.findMany as jest.Mock).mockResolvedValue([
        { id: 'payment_1' },
        { id: 'payment_2' },
      ]);
      (mockPrismaService.agent.findMany as jest.Mock).mockResolvedValue([
        { id: 'agent_1' },
        { id: 'agent_2' },
      ]);
      (
        mockPrismaService.workflow_template.findUnique as jest.Mock
      ).mockResolvedValue(null);

      await expect(
        service.createNewApplication(mockCreateDto, 'admin_123'),
      ).rejects.toThrow('Invalid workflow template ID');

      expect(mockPrismaService.application.create).not.toHaveBeenCalled();
    });
  });

  describe('Application Status Consistency', () => {
    it('should ensure both creation methods use ApplicationStatus.Pending', () => {
      // This test verifies that both methods are configured to use the same status
      expect(ApplicationStatus.Pending).toBe('Pending');

      // Verify the enum contains the expected values
      expect(Object.values(ApplicationStatus)).toContain('Pending');
      expect(Object.values(ApplicationStatus)).toContain('Draft');
      expect(Object.values(ApplicationStatus)).toContain('Submitted');
    });
  });
});
