/**
 * PM2 Ecosystem Configuration
 *
 * Production-ready process management configuration for Career Ireland API services.
 * Includes the main API service and the missing document reminder scheduler.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.1.0
 * @since 2025-07-18
 * @updated 2025-07-19 - Fixed production configuration issues
 */

module.exports = {
  apps: [
    {
      // Main API Service
      name: 'careerireland-api',
      script: 'dist/src/main.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      env: {
        NODE_ENV: 'production',
        PORT: 4242,
      },
      error_file: './logs/api-error.log',
      out_file: './logs/api-out.log',
      log_file: './logs/api-combined.log',
      time: true,
    },

    {
      // Document Reminder Scheduler Service
      name: 'careerireland-scheduler',
      script: 'dist/scripts/document-reminder.js',
      args: '--scheduler',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      min_uptime: '10s',
      max_restarts: 5,
      restart_delay: 4000,
      env: {
        NODE_ENV: 'production',
        SCHEDULER_ENABLED: 'true',
        SCHEDULER_HOUR: '9',
        SCHEDULER_MINUTE: '0',
        SCHEDULER_TIMEZONE: 'UTC',
        SCHEDULER_CHECK_INTERVAL: '60000',
        SCHEDULER_LOG_LEVEL: 'warn',
      },
      error_file: './logs/scheduler-error.log',
      out_file: './logs/scheduler-out.log',
      log_file: './logs/scheduler-combined.log',
      time: true,
    },
  ],
};
