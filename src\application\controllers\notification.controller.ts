/**
 * Notification Controller
 *
 * Handles notification settings management for the Career Ireland platform.
 * This controller provides REST API endpoints for managing user notification
 * preferences across different notification types.
 *
 * Features:
 * - Get user notification settings
 * - Update user notification settings
 * - Proper input validation and error handling
 * - JWT authentication required
 *
 * Security:
 * - JWT authentication for all endpoints
 * - Input validation using DTOs
 * - User-specific settings access control
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-07-10
 */

import {
  Controller,
  Get,
  Put,
  Body,
  UseGuards,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAdmin } from '../../guards/jwt.admin.guard';
import { GetUser } from '../../decorator/user.decorator';
import { IJWTPayload } from '../../types/auth';
import { NotificationService } from '../services/notification.service';
import {
  NotificationSettingsDto,
  UpdateNotificationSettingsDto,
} from '../dto/notification.dto';

/**
 * Notification Controller
 *
 * Provides endpoints for managing user notification settings.
 * All endpoints require JWT authentication.
 */
@ApiTags('notifications')
@Controller('notifications')
@UseGuards(JwtAdmin)
@ApiBearerAuth()
export class NotificationController {
  private readonly logger = new Logger(NotificationController.name);

  /**
   * Notification Controller Constructor
   *
   * Initializes the notification controller with the notification service dependency.
   *
   * @param notificationService - Notification service instance for handling notification operations
   */
  constructor(private readonly notificationService: NotificationService) {}

  /**
   * Get User Notification Settings
   *
   * Retrieves the current notification settings for the authenticated user.
   * If no settings exist, default settings are created and returned.
   *
   * @param user - JWT payload containing user information
   * @returns Promise<NotificationSettingsDto> - Current notification settings
   */
  @Get('settings')
  @ApiOperation({
    summary: 'Get user notification settings',
    description:
      'Retrieve current notification preferences for the authenticated user. Creates default settings if none exist.',
  })
  @ApiResponse({
    status: 200,
    description: 'Notification settings retrieved successfully',
    type: NotificationSettingsDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getUserNotificationSettings(
    @GetUser() user: IJWTPayload,
  ): Promise<NotificationSettingsDto> {
    try {
      this.logger.log(`Getting notification settings for user: ${user.id}`);

      const settings =
        await this.notificationService.getUserNotificationSettings(user.id);

      this.logger.log(
        `Successfully retrieved notification settings for user: ${user.id}`,
      );
      return settings;
    } catch (error) {
      this.logger.error(
        `Failed to get notification settings for user ${user.id}: ${error.message}`,
        error.stack,
      );

      throw new HttpException(
        {
          success: false,
          message: 'Failed to retrieve notification settings',
          error: 'Internal server error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update User Notification Settings
   *
   * Updates the notification settings for the authenticated user.
   * Only provided fields will be updated; omitted fields remain unchanged.
   *
   * @param user - JWT payload containing user information
   * @param updateDto - Notification settings update data
   * @returns Promise<NotificationSettingsDto> - Updated notification settings
   */
  @Put('settings')
  @ApiOperation({
    summary: 'Update user notification settings',
    description:
      'Update notification preferences for the authenticated user. Only provided fields will be updated.',
  })
  @ApiBody({
    type: UpdateNotificationSettingsDto,
    description: 'Notification settings to update',
    examples: {
      'partial-update': {
        summary: 'Partial Update Example',
        description: 'Update only specific notification preferences',
        value: {
          agent_assigned: false,
          missing_document_reminder_days: 14,
          system_maintenance: false,
        },
      },
      'full-update': {
        summary: 'Full Update Example',
        description: 'Update all notification preferences',
        value: {
          agent_assigned: true,
          case_status_update: true,
          agent_query: true,
          document_rejection: true,
          missing_document_reminder_days: 7,
          system_maintenance: true,
          upcoming_deadline_alerts: true,
          final_decision_issued: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Notification settings updated successfully',
    type: NotificationSettingsDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async updateUserNotificationSettings(
    @GetUser() user: IJWTPayload,
    @Body() updateDto: UpdateNotificationSettingsDto,
  ): Promise<NotificationSettingsDto> {
    try {
      this.logger.log(`Updating notification settings for user: ${user.id}`);

      // Validate that at least one field is provided
      const hasValidFields = Object.keys(updateDto).some(
        (key) => updateDto[key] !== undefined,
      );

      if (!hasValidFields) {
        throw new HttpException(
          {
            success: false,
            message: 'At least one notification setting field must be provided',
            error: 'Validation failed',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const updatedSettings =
        await this.notificationService.updateUserNotificationSettings(
          user.id,
          updateDto,
        );

      this.logger.log(
        `Successfully updated notification settings for user: ${user.id}`,
      );
      return updatedSettings;
    } catch (error) {
      this.logger.error(
        `Failed to update notification settings for user ${user.id}: ${error.message}`,
        error.stack,
      );

      // Re-throw HttpExceptions as-is
      if (error instanceof HttpException) {
        throw error;
      }

      // Handle validation errors
      if (
        error.message &&
        (error.message.includes('validation') ||
          error.message.includes(
            'Missing document reminder days must be between 1 and 365 days',
          ))
      ) {
        throw new HttpException(
          {
            success: false,
            message: 'Invalid notification settings provided',
            error: error.message,
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Generic server error
      throw new HttpException(
        {
          success: false,
          message: 'Failed to update notification settings',
          error: 'Internal server error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
