import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentStatus, DocumentType } from '@prisma/client';

describe('DocumentVaultService', () => {
  let service: DocumentVaultService;
  let prismaService: jest.Mocked<PrismaService>;
  let mediaService: jest.Mocked<MediaService>;

  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test-document.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024 * 1024, // 1MB
    buffer: Buffer.from('mock file content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  };

  const mockDocument = {
    id: 'doc-123',
    document_name: 'Test Document',
    original_filename: 'test-document.pdf',
    document_type: DocumentType.Passport,
    document_category: 'Identity',
    file_path: 'documents/test-document.pdf',
    file_size: 1024 * 1024,
    file_hash: 'abc123hash',
    mime_type: 'application/pdf',
    status: DocumentStatus.Pending,
    version: '1.0',
    is_current_version: true,
    parent_document_id: null,
    user_id: 'user-123',
    guest_email: null,
    expiry_date: null,
    tags: ['passport', 'identity'],
    metadata: {},
    uploaded_by: 'user-123',
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const mockPrismaService = {
      document_vault: {
        create: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        update: jest.fn(),
        updateMany: jest.fn(),
        count: jest.fn(),
      },
      user: {
        findUnique: jest.fn(),
      },
      application: {
        findUnique: jest.fn(),
      },
      application_document: {
        create: jest.fn(),
      },
    };

    const mockMediaService = {
      uploadFile: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentVaultService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: MediaService,
          useValue: mockMediaService,
        },
      ],
    }).compile();

    service = module.get<DocumentVaultService>(DocumentVaultService);
    prismaService = module.get(PrismaService);
    mediaService = module.get(MediaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('uploadDocument', () => {
    const uploadMetadata = {
      document_name: 'Test Document',
      document_type: DocumentType.Passport,
      document_category: 'Identity',
      tags: ['passport', 'identity'],
    };

    beforeEach(() => {
      mediaService.uploadFile.mockResolvedValue({
        status: 'OK',
        url: 'documents/test-document.pdf',
      });
      prismaService.document_vault.findFirst.mockResolvedValue(null); // No duplicates
      prismaService.document_vault.create.mockResolvedValue(mockDocument);
      // Mock user validation - user exists by default
      prismaService.user.findUnique.mockResolvedValue({ id: 'user-123' });
    });

    it('should upload document successfully', async () => {
      const result = await service.uploadDocument(
        mockFile,
        uploadMetadata,
        'user-123',
      );

      expect(result).toEqual(mockDocument);
      expect(mediaService.uploadFile).toHaveBeenCalledWith(
        mockFile,
        'documents',
      );
      expect(prismaService.document_vault.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          document_name: uploadMetadata.document_name,
          document_type: uploadMetadata.document_type,
          user_id: 'user-123',
          file_size: mockFile.size,
          mime_type: mockFile.mimetype,
        }),
      });
    });

    it('should throw BadRequestException for duplicate document', async () => {
      prismaService.document_vault.findFirst.mockResolvedValue(mockDocument);

      await expect(
        service.uploadDocument(mockFile, uploadMetadata, 'user-123'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should validate file size limit', async () => {
      const largeFile = { ...mockFile, size: 30 * 1024 * 1024 }; // 30MB

      await expect(
        service.uploadDocument(largeFile, uploadMetadata, 'user-123'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should validate file type', async () => {
      const invalidFile = { ...mockFile, mimetype: 'application/exe' };

      await expect(
        service.uploadDocument(invalidFile, uploadMetadata, 'user-123'),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle guest uploads', async () => {
      const guestMetadata = {
        ...uploadMetadata,
        guest_email: '<EMAIL>',
      };

      await service.uploadDocument(mockFile, guestMetadata);

      expect(prismaService.document_vault.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          user_id: undefined,
          guest_email: '<EMAIL>',
        }),
      });
    });

    it('should fallback to guest upload when user does not exist', async () => {
      // Mock user not found
      prismaService.user.findUnique.mockResolvedValue(null);

      const result = await service.uploadDocument(
        mockFile,
        uploadMetadata,
        'non-existent-user',
      );

      expect(result).toEqual(mockDocument);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'non-existent-user' },
        select: { id: true },
      });
      expect(prismaService.document_vault.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          user_id: undefined, // Should be null since user doesn't exist
          guest_email: '<EMAIL>',
          uploaded_by: 'non-existent-user', // Keep original for audit trail
        }),
      });
    });
  });

  describe('getUserVault', () => {
    beforeEach(() => {
      prismaService.document_vault.findMany.mockResolvedValue([mockDocument]);
    });

    it('should get user documents with default filters', async () => {
      const result = await service.getUserVault('user-123');

      expect(result).toEqual([mockDocument]);
      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          user_id: 'user-123',
          is_current_version: true,
        },
        orderBy: { created_at: 'desc' },
        skip: 0,
        take: 20,
      });
    });

    it('should apply document type filter', async () => {
      await service.getUserVault('user-123', {
        document_type: DocumentType.Passport,
      });

      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          user_id: 'user-123',
          is_current_version: true,
          document_type: DocumentType.Passport,
        },
        orderBy: { created_at: 'desc' },
        skip: 0,
        take: 20,
      });
    });

    it('should apply search filter', async () => {
      await service.getUserVault('user-123', {
        search: 'passport',
      });

      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          user_id: 'user-123',
          is_current_version: true,
          OR: [
            { document_name: { contains: 'passport', mode: 'insensitive' } },
            {
              original_filename: { contains: 'passport', mode: 'insensitive' },
            },
            {
              document_category: { contains: 'passport', mode: 'insensitive' },
            },
          ],
        },
        orderBy: { created_at: 'desc' },
        skip: 0,
        take: 20,
      });
    });

    it('should apply pagination', async () => {
      await service.getUserVault('user-123', {
        page: 2,
        limit: 10,
      });

      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          user_id: 'user-123',
          is_current_version: true,
        },
        orderBy: { created_at: 'desc' },
        skip: 10,
        take: 10,
      });
    });
  });

  describe('checkExpiringDocuments', () => {
    it('should find expiring documents', async () => {
      const expiringDoc = {
        ...mockDocument,
        expiry_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
      };
      prismaService.document_vault.findMany.mockResolvedValue([expiringDoc]);

      const result = await service.checkExpiringDocuments(30);

      expect(result).toEqual([expiringDoc]);
      expect(prismaService.document_vault.findMany).toHaveBeenCalledWith({
        where: {
          expiry_date: {
            lte: expect.any(Date),
            gte: expect.any(Date),
          },
          expiry_reminder_sent: false,
          is_current_version: true,
        },
        orderBy: { expiry_date: 'asc' },
      });
    });
  });

  describe('linkDocumentToApplication', () => {
    beforeEach(() => {
      prismaService.document_vault.findUnique.mockResolvedValue(mockDocument);
      prismaService.application.findUnique.mockResolvedValue({
        id: 'app-123',
        application_number: 'APP-001',
      });
      prismaService.application_document.create.mockResolvedValue({});
    });

    it('should link document to application successfully', async () => {
      await service.linkDocumentToApplication('doc-123', 'app-123');

      expect(prismaService.application_document.create).toHaveBeenCalledWith({
        data: {
          application_id: 'app-123',
          document_vault_id: 'doc-123',
          document_requirement: mockDocument.document_name,
          submission_status: mockDocument.status,
        },
      });
    });

    it('should throw NotFoundException for invalid document', async () => {
      prismaService.document_vault.findUnique.mockResolvedValue(null);

      await expect(
        service.linkDocumentToApplication('invalid-doc', 'app-123'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException for invalid application', async () => {
      prismaService.application.findUnique.mockResolvedValue(null);

      await expect(
        service.linkDocumentToApplication('doc-123', 'invalid-app'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('calculateFileHash', () => {
    it('should calculate SHA-256 hash correctly', () => {
      const buffer = Buffer.from('test content');
      const hash = service.calculateFileHash(buffer);

      expect(hash).toBe(
        '1eebdf4fdc9fc7bf283031b93f9aef3338de9052f584b10f4e4c6c1c921b4a06',
      );
      expect(typeof hash).toBe('string');
      expect(hash.length).toBe(64); // SHA-256 produces 64-character hex string
    });
  });
});
