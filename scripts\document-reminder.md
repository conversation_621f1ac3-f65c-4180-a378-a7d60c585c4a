# Missing Document Reminder System

A single, self-contained document reminder system that automatically sends email notifications to users about missing required documents.

## Overview

The Missing Document Reminder System replaces the previous multi-file implementation with a single, comprehensive script that includes all functionality:

- **Single Script**: `scripts/document-reminder.ts` contains all functionality
- **Self-contained**: No external dependencies or complex file structures
- **Comprehensive**: Includes database queries, email sending, scheduling, and logging

## Key Features

### Core Functionality
1. **Document Analysis**: Reads and analyzes missing required documents for each user/application
2. **Email Notification**: Sends informative emails to users listing their missing documents with clear instructions
3. **Completion Timeline**: Includes 7-day completion timeline in emails once all required documents are uploaded
4. **Configurable Intervals**: Uses `missing_document_reminder_days` from `config/notification-settings.json` (currently 10 days)
5. **Self-contained Scheduling**: Internal scheduling mechanism (no external cron jobs required)

### Technical Features
- **Array Operators**: Uses proper database array operators (has, hasSome, hasEvery) instead of equality operators
- **React Email Rendering**: Integrates with existing `application-requirements.tsx` template pattern
- **Fallback Templates**: Includes HTML fallback templates for error scenarios
- **Non-blocking Email Sending**: Asynchronous email processing with error recovery
- **File-based Logging**: Comprehensive logging to `logs/document-reminders/` directory
- **File-based Configuration**: Uses JSON configuration files for settings

## Usage

### One-time Execution
```bash
npm run reminder:documents
```

### Scheduler Mode (Long-running Process)
```bash
npm run reminder:documents -- --scheduler
```

### Status Check
```bash
npm run reminder:documents -- --status
```

### Help
```bash
npm run reminder:documents -- --help
```

## Environment Variables

Configure the scheduler using environment variables:

```bash
SCHEDULER_ENABLED=true/false          # Enable/disable scheduler
SCHEDULER_HOUR=9                      # Hour to run (0-23)
SCHEDULER_MINUTE=0                    # Minute to run (0-59)
SCHEDULER_TIMEZONE=UTC                # Timezone for scheduling
SCHEDULER_CHECK_INTERVAL=60000        # Check interval in milliseconds
SCHEDULER_LOG_LEVEL=info              # Log level (debug/info/warn/error)
```

## Configuration

### Notification Settings

Users can configure their reminder frequency in `config/notification-settings.json`:

```json
{
  "user_id": "user-123",
  "missing_document_reminder_days": 10,
  "agent_assigned": true,
  "case_status_update": true,
  "document_rejection": true
}
```

### Email Template

The system uses the existing `src/template/missing-document-reminder.tsx` React email template with fallback HTML templates for error scenarios.

## Database Queries

The system uses proper Prisma array operators for efficient database queries:

```typescript
// Query applications with missing documents
const applications = await prisma.application.findMany({
  where: {
    status: { not: ApplicationStatus.Completed },
    documents: {
      some: {
        OR: [
          { status: 'pending' },
          { status: 'rejected' },
          { file_url: '' },
        ],
      },
    },
  },
  include: {
    user: { select: { id: true, name: true, email: true } },
    documents: {
      where: {
        OR: [
          { status: 'pending' },
          { status: 'rejected' },
          { file_url: '' },
        ],
      },
      orderBy: { stage_order: 'asc' },
    },
  },
});
```

## Testing

### Run Unit Tests
```bash
npm run test:document-reminder
```

### Run Tests with Coverage
```bash
npm run test:document-reminder:cov
```

### Watch Mode
```bash
npm run test:document-reminder:watch
```

## Logging

The system creates comprehensive logs in `logs/document-reminders/` directory:

- **File Format**: JSON-formatted log entries
- **Daily Rotation**: New log file created each day (YYYY-MM-DD.log)
- **Log Levels**: info, warn, error, debug
- **Context**: Each log entry includes timestamp, level, context, and message

Example log entry:
```json
{
  "timestamp": "2025-07-18T10:30:00.000Z",
  "level": "info",
  "context": "DocumentReminder",
  "message": "Email sent <NAME_EMAIL> for application APP-001"
}
```

## Error Handling

The system includes comprehensive error handling:

1. **Database Errors**: Graceful handling of connection failures and query errors
2. **Email Failures**: Fallback templates when React email rendering fails
3. **Configuration Errors**: Default values when settings cannot be read
4. **Scheduler Errors**: Automatic recovery and continued operation

## Performance Considerations

- **Minimal Memory Usage**: Clean, minimal code without over-engineering
- **Efficient Queries**: Uses proper database indexing and array operators
- **Non-blocking Operations**: Asynchronous email sending prevents blocking
- **Resource Management**: Proper cleanup and memory management

## Migration from Previous System

This consolidated system replaces the previous multi-file implementation:

- **Previous**: Multiple files (`missing-document-reminder.ts`, `missing-document-reminder.service.ts`, `scheduler/missing-document-scheduler.ts`, etc.)
- **Current**: Single file (`document-reminder.ts`) with all functionality

### Benefits of Consolidation

1. **Simplified Deployment**: Single file to deploy and manage
2. **Reduced Complexity**: No complex inter-file dependencies
3. **Easier Maintenance**: All logic in one place
4. **Better Testing**: Comprehensive test coverage in one test suite
5. **Improved Performance**: Reduced overhead from multiple service layers

## Security Considerations

- **Input Validation**: All user inputs are validated and sanitized
- **Email Security**: Secure email headers and content filtering
- **Error Masking**: No sensitive information exposed in error messages
- **Access Control**: Proper authentication and authorization checks
- **Logging Security**: No PII or sensitive data in log files

## Deployment

### Production Deployment
```bash
# Build the application
npm run build

# Run the reminder system
npm run reminder:documents

# Or run as scheduler
npm run reminder:documents -- --scheduler
```

### PM2 Configuration
```javascript
module.exports = {
  apps: [
    {
      name: 'document-reminder-scheduler',
      script: 'scripts/document-reminder.ts',
      args: '--scheduler',
      interpreter: 'ts-node',
      env: {
        SCHEDULER_ENABLED: 'true',
        SCHEDULER_HOUR: '9',
        SCHEDULER_MINUTE: '0',
      },
    },
  ],
};
```

## Support

For issues or questions regarding the document reminder system:

1. Check the log files in `logs/document-reminders/`
2. Review the configuration in `config/notification-settings.json`
3. Run the system with `--help` flag for usage information
4. Consult the unit tests for expected behavior examples

## Version History

- **v1.0.0** (2025-07-18): Initial implementation
  - Single self-contained script
  - Comprehensive unit test coverage
  - React email template integration
  - File-based configuration and logging
  - Self-contained scheduling mechanism
