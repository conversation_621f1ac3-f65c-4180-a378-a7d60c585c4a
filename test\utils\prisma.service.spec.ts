/**
 * Prisma Service Tests
 *
 * Comprehensive test suite for PrismaService.
 * Tests database connection initialization and lifecycle management.
 *
 * @desc Tests the Prisma database service initialization
 * @assumptions Uses mocked PrismaClient for database testing
 * @mocked_dependencies PrismaClient
 */

import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../../src/utils/prisma.service';

// Mock PrismaClient
const mockPrismaClient = {
  $connect: jest.fn(),
  $disconnect: jest.fn(),
};

// Mock the PrismaClient constructor
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient),
}));

describe('PrismaService', () => {
  let service: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PrismaService],
    }).compile();

    service = module.get<PrismaService>(PrismaService);
    
    // Reset mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should extend PrismaClient', () => {
      expect(service).toBeInstanceOf(PrismaService);
    });
  });

  describe('onModuleInit', () => {
    it('should call $connect on module initialization', async () => {
      // Arrange
      mockPrismaClient.$connect.mockResolvedValue(undefined);

      // Act
      await service.onModuleInit();

      // Assert
      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(1);
    });

    it('should handle connection errors gracefully', async () => {
      // Arrange
      const connectionError = new Error('Database connection failed');
      mockPrismaClient.$connect.mockRejectedValue(connectionError);

      // Act & Assert
      await expect(service.onModuleInit()).rejects.toThrow(
        'Database connection failed',
      );
    });

    it('should propagate connection timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Connection timeout');
      mockPrismaClient.$connect.mockRejectedValue(timeoutError);

      // Act & Assert
      await expect(service.onModuleInit()).rejects.toThrow(
        'Connection timeout',
      );
    });
  });

  describe('Database Connection Management', () => {
    it('should maintain connection state', async () => {
      // Arrange
      mockPrismaClient.$connect.mockResolvedValue(undefined);

      // Act
      await service.onModuleInit();

      // Assert
      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(1);
    });

    it('should handle multiple initialization calls', async () => {
      // Arrange
      mockPrismaClient.$connect.mockResolvedValue(undefined);

      // Act
      await service.onModuleInit();
      await service.onModuleInit();

      // Assert
      expect(mockPrismaClient.$connect).toHaveBeenCalledTimes(2);
    });
  });

  describe('Service Lifecycle', () => {
    it('should implement OnModuleInit interface', () => {
      expect(service.onModuleInit).toBeDefined();
      expect(typeof service.onModuleInit).toBe('function');
    });

    it('should return a promise from onModuleInit', () => {
      mockPrismaClient.$connect.mockResolvedValue(undefined);
      const result = service.onModuleInit();
      expect(result).toBeInstanceOf(Promise);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors during connection', async () => {
      // Arrange
      const networkError = new Error('ECONNREFUSED');
      mockPrismaClient.$connect.mockRejectedValue(networkError);

      // Act & Assert
      await expect(service.onModuleInit()).rejects.toThrow('ECONNREFUSED');
    });

    it('should handle authentication errors', async () => {
      // Arrange
      const authError = new Error('Authentication failed');
      mockPrismaClient.$connect.mockRejectedValue(authError);

      // Act & Assert
      await expect(service.onModuleInit()).rejects.toThrow(
        'Authentication failed',
      );
    });

    it('should handle database not found errors', async () => {
      // Arrange
      const dbError = new Error('Database does not exist');
      mockPrismaClient.$connect.mockRejectedValue(dbError);

      // Act & Assert
      await expect(service.onModuleInit()).rejects.toThrow(
        'Database does not exist',
      );
    });
  });
});
