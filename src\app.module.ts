import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { UserModule } from './user/user.module';
import { PrismaService } from './utils/prisma.service';
import { MentorService } from './mentor/mentor.service';
import { MentorModule } from './mentor/mentor.module';
import { JwtService } from '@nestjs/jwt';
import { MailerModule } from './mailer/mailer.module';
import { OtpService } from './otp/otp.service';
import { OtpModule } from './otp/otp.module';
import { MailerService } from './mailer/mailer.service';
import { MediaModule } from './media/media.module';
import { FastifyMulterModule } from '@nest-lab/fastify-multer';
import { AdminModule } from './admin/admin.module';
import { ServicesModule } from './services/services.module';
import { ResumeModule } from './resume/resume.module';
import { ResumeBuilderModule } from './resume-builder/resume-builder.module';
import { APP_FILTER } from '@nestjs/core';
import { UnknownExceptionFilter } from './error/unknown-exception.filter';
import { ReviewService } from './review/review.service';
import { ReviewModule } from './review/review.module';
import { ContactUsModule } from './contact-us/contact-us.module';
import { BlogModule } from './blog/blog.module';
import { PaymentModule } from './payment/payment.module';
import { PackagesModule } from './packages/packages.module';
import { ImmigrationModule } from './immigration/immigration.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { PasswordModule } from './password/password.module';
import { TrainingModule } from './training/training.module';
import { GuestModule } from './guest/guest.module';
import { CustomerReviewModule } from './customer-review/customer-review.module';
import { CommentModule } from './comment/comment.module';
import { ApplicationModule } from './application/application.module';
import { DocumentModule } from './application/modules/document.module';
import { DocumentMasterModule } from './document/document-master.module';
import { WorkflowMasterModule } from './workflow/workflow-master.module';
import { WorkflowTemplateModule } from './workflow-template/workflow-template.module';
import { AgentModule } from './agent/agent.module';
import { AuthModule } from './auth/auth.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    UserModule,
    MentorModule,
    MailerModule,
    OtpModule,
    MediaModule,
    FastifyMulterModule,

    AdminModule,

    // Agent Management Module
    AgentModule,

    // Universal Authentication Module
    AuthModule,

    ServicesModule,

    ResumeModule,

    ResumeBuilderModule,

    ReviewModule,

    ContactUsModule,

    BlogModule,

    PaymentModule,

    PackagesModule,

    ImmigrationModule,

    DashboardModule,

    PasswordModule,

    TrainingModule,

    GuestModule,

    CustomerReviewModule,

    CommentModule,

    // Task 2: Core Service Abstractions Implementation
    ApplicationModule,

    // Document Management Module (Document Vault)
    DocumentModule,

    // Document Master CRUD Module
    DocumentMasterModule,

    // Workflow Master CRUD Module
    WorkflowMasterModule,

    // Workflow Template CRUD Module
    WorkflowTemplateModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    PrismaService,
    MentorService,
    JwtService,
    OtpService,
    MailerService,
    {
      provide: APP_FILTER,
      useClass: UnknownExceptionFilter,
    },
    ReviewService,
  ],
})
export class AppModule {}
