/**
 * Workflow Template DTOs
 *
 * This file contains Data Transfer Objects (DTOs) for workflow template operations.
 * These DTOs provide validation, documentation, and type safety for API endpoints.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsArray,
  IsEnum,
  IsInt,
  Min,
  Max,
  Length,
  MaxLength,
  ValidateNested,
  IsIn,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ServiceType } from '../interfaces/workflow-template.interface';

/**
 * Document Configuration DTO
 */
export class DocumentConfigDto {
  @ApiProperty({
    description: 'Document name',
    example: 'passport',
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 100)
  documentName: string;

  @ApiProperty({
    description: 'Whether the document is required',
    example: true,
  })
  @IsBoolean()
  required: boolean;
}

/**
 * Custom Form Field DTO
 */
export class CustomFormFieldDto {
  @ApiProperty({
    description: 'Field name',
    example: 'name',
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 50)
  fieldName: string;

  @ApiProperty({
    description: 'Field type',
    example: 'text',
    enum: ['text', 'number', 'email', 'date', 'select', 'textarea', 'checkbox'],
  })
  @IsString()
  @IsIn(['text', 'number', 'email', 'date', 'select', 'textarea', 'checkbox'])
  fieldType: string;

  @ApiProperty({
    description: 'Whether the field is required',
    example: true,
  })
  @IsBoolean()
  required: boolean;

  @ApiPropertyOptional({
    description: 'Options for select fields',
    example: ['option1', 'option2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  options?: string[];
  @ApiPropertyOptional({
    description: 'Show to client field',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showToClient?: boolean;
}

/**
 * Workflow Stage DTO
 */
export class WorkflowStageDto {
  @ApiProperty({
    description: 'Stage name',
    example: 'Document Collection',
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 100)
  stageName: string;

  @ApiProperty({
    description: 'Stage order (1-based)',
    example: 1,
    minimum: 1,
    maximum: 50,
  })
  @IsInt()
  @Min(1)
  @Max(50)
  stageOrder: number;

  @ApiPropertyOptional({
    description: 'Stage description',
    example: 'Collect all required documents from applicant',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    description: 'Whether custom form fields are required for this stage',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  cutomFormRequired?: boolean;

  @ApiPropertyOptional({
    description: 'Whether documents are required for this stage',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  documentsRequired?: boolean;

  @ApiPropertyOptional({
    description: 'List of documents required for this stage',
    type: [DocumentConfigDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DocumentConfigDto)
  documents?: DocumentConfigDto[];

  @ApiPropertyOptional({
    description: 'Whether custom form is required for this stage',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  customFormRequired?: boolean;

  @ApiPropertyOptional({
    description: 'Custom form fields for this stage',
    type: [CustomFormFieldDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFormFieldDto)
  customForm?: CustomFormFieldDto[];

  // @ApiPropertyOptional({
  //   description: 'Assignee role for this stage',
  //   example: 'admin',
  //   enum: ['admin', 'applicant', 'reviewer'],
  // })
  // @IsOptional()
  // @IsString()
  // @IsIn(['admin', 'applicant', 'reviewer'])
  // assigneeRole?: string;

  @ApiPropertyOptional({
    description: 'Whether stage auto-advances upon completion',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  autoAdvance?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this stage should be visible to clients',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  showToClient?: boolean;
}

/**
 * Create Workflow Template DTO
 */
export class CreateWorkflowTemplateDto {
  @ApiProperty({
    description: 'Workflow template name',
    example: 'Standard Immigration Application Workflow',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  name: string;

  @ApiPropertyOptional({
    description: 'Workflow template description',
    example: 'Complete workflow template for immigration applications',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Service type',
    example: 'immigration',
    enum: ['immigration', 'training', 'packages'],
  })
  @IsString()
  @IsEnum(['immigration', 'training', 'packages'])
  serviceType: ServiceType;

  @ApiPropertyOptional({
    description: 'Service ID reference',
    example: 'service_123',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  serviceId?: string;

  @ApiPropertyOptional({
    description: 'Whether this workflow template is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this is the default template for the service',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @ApiProperty({
    description: 'Workflow template stages configuration',
    type: [WorkflowStageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkflowStageDto)
  workflowTemplate: WorkflowStageDto[];
}

/**
 * Update Workflow Template DTO
 */
export class UpdateWorkflowTemplateDto extends PartialType(
  CreateWorkflowTemplateDto,
) {}

/**
 * Set Default Template DTO
 */
export class SetDefaultTemplateDto {
  @ApiProperty({
    description: 'Whether to set this template as default',
    example: true,
  })
  @IsBoolean()
  isDefault: boolean;
}

/**
 * Workflow Template Filters DTO
 */
export class WorkflowTemplateFiltersDto {
  @ApiPropertyOptional({
    description: 'Search by name (case-insensitive)',
    example: 'immigration',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by service type',
    example: 'immigration',
    enum: ['immigration', 'training', 'packages', 'consulting'],
  })
  @IsOptional()
  @IsEnum(['immigration', 'training', 'packages', 'consulting'])
  serviceType?: ServiceType;

  @ApiPropertyOptional({
    description:
      'Filter by service ID (e.g., immigration product ID when serviceType is immigration)',
    example: 'clx1234567890abcdef',
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  serviceId?: string;

  @ApiPropertyOptional({
    description: 'Filter by active status',
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Page number (1-based)',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'name',
    enum: ['name', 'serviceType', 'createdAt', 'updatedAt'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['name', 'serviceType', 'createdAt', 'updatedAt'])
  sortBy?: string = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * Workflow Template Response DTO
 */
export class WorkflowTemplateResponseDto {
  @ApiProperty({
    description: 'Workflow template ID',
    example: 'clx1234567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'Workflow template name',
    example: 'Standard Immigration Application Workflow',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Workflow template description',
    example: 'Complete workflow template for immigration applications',
  })
  description?: string;

  @ApiProperty({
    description: 'Service type',
    example: 'immigration',
    enum: ['immigration', 'training', 'packages', 'consulting'],
  })
  serviceType: ServiceType;

  @ApiPropertyOptional({
    description: 'Service ID reference',
    example: 'service_123',
  })
  serviceId?: string;

  @ApiProperty({
    description: 'Whether this workflow template is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Whether this is the default template for the service',
    example: false,
  })
  isDefault: boolean;

  @ApiPropertyOptional({
    description: 'Package name (only for package service type)',
    example: 'Premium Immigration Package',
  })
  packageName?: string;

  @ApiProperty({
    description: 'Workflow template stages configuration',
    type: [WorkflowStageDto],
  })
  workflowTemplate: WorkflowStageDto[];

  @ApiPropertyOptional({
    description: 'Admin user who created this template',
    example: 'John Smith',
  })
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Admin user who last updated this template',
    example: 'Jane Doe',
  })
  updatedBy?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-01-06T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-01-06T10:30:00.000Z',
  })
  updatedAt: Date;
}

/**
 * Paginated Workflow Template Response DTO
 */
export class PaginatedWorkflowTemplateResponseDto {
  @ApiProperty({
    description: 'Array of workflow templates',
    type: [WorkflowTemplateResponseDto],
  })
  data: WorkflowTemplateResponseDto[];

  @ApiProperty({
    description: 'Total number of workflow templates',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages: number;
}

/**
 * Workflow Template Usage Response DTO
 */
export class WorkflowTemplateUsageResponseDto {
  @ApiProperty({
    description: 'Workflow template ID',
    example: 'clx1234567890abcdef',
  })
  templateId: string;

  @ApiProperty({
    description: 'Number of active applications using this template',
    example: 5,
  })
  activeApplications: number;

  @ApiProperty({
    description: 'Total number of applications that have used this template',
    example: 15,
  })
  totalApplications: number;

  @ApiProperty({
    description: 'Whether this template can be safely deleted',
    example: false,
  })
  canDelete: boolean;

  @ApiProperty({
    description: 'Usage details by service type',
    example: [
      { serviceType: 'immigration', count: 10 },
      { serviceType: 'training', count: 5 },
    ],
  })
  usageDetails: {
    serviceType: string;
    count: number;
  }[];
}
