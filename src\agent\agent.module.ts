import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AgentController } from './agent.controller';
import { AgentService } from './agent.service';
import { PrismaService } from '../utils/prisma.service';
import { MailerService } from '../mailer/mailer.service';
import { LoggerService } from '../utils/logger.service';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [AgentController],
  providers: [AgentService, PrismaService, MailerService, LoggerService],
  exports: [AgentService], // Export AgentService for use in other modules
})
export class AgentModule {}
