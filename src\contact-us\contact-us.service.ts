import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { ContactUsDto } from './dto/contact-us.dto';
import { MailerService } from 'src/mailer/mailer.service';
import { render } from '@react-email/components';
import ContactFormEmail from 'src/template/contact';

@Injectable()
export class ContactUsService {
  constructor(
    private prisma: PrismaService,
    private mailer: MailerService,
  ) {}

  async create(dto: ContactUsDto) {
    const contactUs = await this.prisma.contact_us.create({
      data: dto,
    });

    if (contactUs) {
      await this.mailer.sendEmail({
        from: process.env.EMAIL,
        to: process.env.EMAIL,
        subject: 'New Contact Form Submission',
        cc: [],
        html: await render(ContactFormEmail(contactUs)),
      });
    }

    return contactUs;
  }
  async getAll() {
    const contactUs = await this.prisma.contact_us.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });

    return contactUs;
  }
}
