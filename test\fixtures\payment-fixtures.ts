/**
 * Payment Test Fixtures
 *
 * Test data fixtures for payment module tests.
 * Provides consistent test data across all test files.
 * Includes admin payment functionality test data.
 */

import { Status } from '@prisma/client';
import {
  CreateUnifiedPaymentDto,
  CreateGuestPaymentDto,
  CreateUserPaymentDto,
  PaymentFiltersDto,
  ServiceType,
  PaymentType,
  AdminPaymentProgressDto,
  AdminPaymentStatusDto,
  AdminBulkPaymentUpdateDto,
  AdminPaymentResponseDto,
} from '../../src/payment/dto/payment.dto';

/**
 * Valid User Payment DTO
 * @desc Test data for authenticated user payment creation
 */
export const validUserPaymentDto: CreateUnifiedPaymentDto = {
  serviceType: ServiceType.SERVICE,
  serviceId: 'service_test_123',
  paymentType: PaymentType.USER,
};

/**
 * Valid Guest Payment DTO
 * @desc Test data for guest user payment creation
 */
export const validGuestPaymentDto: CreateUnifiedPaymentDto = {
  serviceType: ServiceType.SERVICE,
  serviceId: 'service_test_123',
  paymentType: PaymentType.GUEST,
  name: '<PERSON>',
  email: '<EMAIL>',
  mobile: '+353123456789',
};

/**
 * Valid Package Payment DTO
 * @desc Test data for package payment creation
 */
export const validPackagePaymentDto: CreateUnifiedPaymentDto = {
  serviceType: ServiceType.PACKAGE,
  serviceId: 'package_test_123',
  paymentType: PaymentType.USER,
};

/**
 * Valid Immigration Service Payment DTO
 * @desc Test data for immigration service payment creation
 */
export const validImmigrationPaymentDto: CreateUnifiedPaymentDto = {
  serviceType: ServiceType.IMMIGRATION,
  serviceId: 'immigration_test_123',
  paymentType: PaymentType.USER,
};

/**
 * Valid Training Payment DTO
 * @desc Test data for training payment creation
 */
export const validTrainingPaymentDto: CreateUnifiedPaymentDto = {
  serviceType: ServiceType.TRAINING,
  serviceId: 'training_test_123',
  paymentType: PaymentType.USER,
};

/**
 * Strict Guest Payment DTO
 * @desc Test data for strict guest payment endpoint validation
 */
export const strictGuestPaymentDto: CreateGuestPaymentDto = {
  name: 'Rohan',
  email: '<EMAIL>',
  mobile: '09749155545',
  serviceType: ServiceType.SERVICE,
  serviceId: 'p1',
};

/**
 * Strict User Payment DTO
 * @desc Test data for strict user payment endpoint validation
 */
export const strictUserPaymentDto: CreateUserPaymentDto = {
  serviceType: ServiceType.SERVICE,
  serviceId: 'p1',
};

/**
 * Invalid Payment DTOs for validation testing
 */
export const invalidPaymentDtos = {
  // Missing required fields
  missingServiceType: {
    serviceId: 'service_test_123',
    paymentType: PaymentType.USER,
  },
  missingServiceId: {
    serviceType: ServiceType.SERVICE,
    paymentType: PaymentType.USER,
  },
  missingPaymentType: {
    serviceType: ServiceType.SERVICE,
    serviceId: 'service_test_123',
  },
  // Invalid enum values
  invalidServiceType: {
    serviceType: 'invalid_service_type',
    serviceId: 'service_test_123',
    paymentType: PaymentType.USER,
  },
  invalidPaymentType: {
    serviceType: ServiceType.SERVICE,
    serviceId: 'service_test_123',
    paymentType: 'invalid_payment_type',
  },
  // Guest payment missing required guest info
  guestMissingName: {
    serviceType: ServiceType.SERVICE,
    serviceId: 'service_test_123',
    paymentType: PaymentType.GUEST,
    email: '<EMAIL>',
    mobile: '+353123456789',
  },
  guestMissingEmail: {
    serviceType: ServiceType.SERVICE,
    serviceId: 'service_test_123',
    paymentType: PaymentType.GUEST,
    name: 'John Doe',
    mobile: '+353123456789',
  },
  guestInvalidEmail: {
    serviceType: ServiceType.SERVICE,
    serviceId: 'service_test_123',
    paymentType: PaymentType.GUEST,
    name: 'John Doe',
    email: 'invalid-email',
    mobile: '+353123456789',
  },
};

/**
 * Payment Filters for testing payment history endpoint
 */
export const validPaymentFilters: PaymentFiltersDto = {
  status: 'paid',
  paymentType: PaymentType.USER,
  serviceType: ServiceType.SERVICE,
  startDate: '2024-01-01',
  endDate: '2024-12-31',
};

/**
 * Empty Payment Filters
 */
export const emptyPaymentFilters: PaymentFiltersDto = {};

/**
 * Pagination Test Filters
 */
export const paginationFilters: PaymentFiltersDto = {
  page: 1,
  limit: 10,
};

export const largePaginationFilters: PaymentFiltersDto = {
  page: 2,
  limit: 50,
};

export const invalidPaginationFilters = {
  page: 0, // Invalid: should be >= 1
  limit: 150, // Invalid: should be <= 100
};

/**
 * Mock Paginated Payment History Response
 */
export const mockPaginatedPaymentHistory = {
  page: 1,
  limit: 10,
  totalPages: 3,
  totalItems: 25,
  data: mockPaymentHistory,
};

/**
 * Mock Webhook Event Data
 * Enhanced with payment method and transaction ID for testing new fields
 */
export const mockWebhookEvent = {
  id: 'evt_test_123',
  object: 'event',
  type: 'checkout.session.completed',
  data: {
    object: {
      id: 'cs_test_123',
      object: 'checkout.session',
      payment_intent: 'pi_test_123',
      payment_status: 'paid',
      payment_method_types: ['card'],
      metadata: {
        serviceType: 'service',
        serviceId: 'service_test_123',
        paymentType: 'user',
        userId: 'user_test_123',
        amount: '10000',
        stripe_session_id: 'cs_test_123',
        stripe_payment_intent_id: 'pi_test_123',
        paymentId: 'payment_test_123',
      },
    },
  },
  created: Math.floor(Date.now() / 1000),
  livemode: false,
};

/**
 * Mock Enhanced Webhook Event with Payment Metadata
 * For testing payment method and transaction ID extraction
 */
export const mockEnhancedWebhookEvent = {
  ...mockWebhookEvent,
  data: {
    object: {
      ...mockWebhookEvent.data.object,
      payment_intent: 'pi_test_enhanced_123',
      payment_method_types: ['card'],
    },
  },
};

/**
 * Mock Payment Analytics Response
 */
export const mockPaymentAnalytics = {
  totalRevenue: 50000,
  totalPayments: 25,
  paymentsByType: [
    {
      payment_type: 'user',
      _sum: { amount: 30000 },
      _count: 15,
    },
    {
      payment_type: 'guest',
      _sum: { amount: 20000 },
      _count: 10,
    },
  ],
  paymentsByService: [
    {
      service_type: 'service',
      _sum: { amount: 25000 },
      _count: 12,
    },
    {
      service_type: 'package',
      _sum: { amount: 15000 },
      _count: 8,
    },
    {
      service_type: 'immigration',
      _sum: { amount: 10000 },
      _count: 5,
    },
  ],
  recentPayments: [
    {
      id: 'payment_1',
      amount: 10000,
      status: 'paid',
      createdAt: new Date(),
      user: { name: 'Test User', email: '<EMAIL>' },
    },
  ],
};

/**
 * Mock Payment History Response
 */
export const mockPaymentHistory = [
  {
    id: 'payment_1',
    amount: 10000,
    status: 'paid',
    payment_type: 'user',
    service_type: 'service',
    createdAt: new Date(),
    user: { name: 'Test User', email: '<EMAIL>' },
    service: { name: 'Test Service' },
  },
  {
    id: 'payment_2',
    amount: 20000,
    status: 'paid',
    payment_type: 'guest',
    service_type: 'package',
    guest_name: 'Guest User',
    guest_email: '<EMAIL>',
    createdAt: new Date(),
    package: { name: 'Test Package' },
  },
];

/**
 * Error Messages for testing
 */
export const errorMessages = {
  authRequired: 'Authentication required for user payments',
  serviceNotFound: 'Service not found',
  invalidPaymentData: 'Invalid payment data',
  stripeError: 'Payment processing failed',
  databaseError: 'Database operation failed',
  emailError: 'Email notification failed',
  paymentNotFound: 'Payment with ID payment_invalid_001 not found',
  invalidPaymentId: 'Invalid payment ID',
  unauthorizedAccess: 'Admin authentication required',
  invalidStatus: 'Invalid status value',
  invalidProgress: 'Invalid progress value',
  bulkUpdateFailed: 'Failed to perform bulk update',
};

// ========================================
// ADMIN PAYMENT TEST DATA
// ========================================

export const mockAdminUser = {
  id: 'admin_test_001',
  name: 'Test Admin',
  email: '<EMAIL>',
  emailVerified: true,
  image: 'https://example.com/admin.jpg',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

export const mockJWTAdminPayload = {
  id: 'admin_test_001',
  email: '<EMAIL>',
  sub: {
    name: 'Test Admin',
  },
};

// ========================================
// ADMIN PAYMENT UPDATE TEST DATA
// ========================================

export const validAdminProgressDto: AdminPaymentProgressDto = {
  paymentId: 'payment_unified_001',
  progress: Status.Active,
};

export const validAdminStatusDto: AdminPaymentStatusDto = {
  paymentId: 'payment_unified_001',
  status: 'paid',
};

export const validBulkUpdateDto: AdminBulkPaymentUpdateDto = {
  paymentIds: ['payment_unified_001', 'payment_unified_002'],
  progress: Status.Active,
  status: 'paid',
};

export const invalidAdminProgressDto: AdminPaymentProgressDto = {
  paymentId: '',
  progress: Status.Active,
};

export const invalidAdminStatusDto: AdminPaymentStatusDto = {
  paymentId: 'payment_unified_001',
  status: '',
};

export const invalidBulkUpdateDto: AdminBulkPaymentUpdateDto = {
  paymentIds: [],
  progress: Status.Active,
};

// ========================================
// MOCK UNIFIED PAYMENT DATA
// ========================================

export const mockUnifiedPayment = {
  id: 'payment_unified_001',
  amount: 50000,
  status: 'paid',
  payment_type: 'user',
  service_type: 'service',
  progress: Status.Pending,
  userId: 'user_test_001',
  serviceId: 'service_test_001',
  packageId: null,
  immigration_serviceId: null,
  trainingId: null,
  guest_name: null,
  guest_email: null,
  guest_mobile: null,
  stripe_session_id: 'cs_test_123',
  stripe_payment_intent_id: 'pi_test_123',
  payment_method: 'card',
  transaction_id: 'ch_test_123',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  user: {
    id: 'user_test_001',
    name: 'Test User',
    email: '<EMAIL>',
  },
  service: {
    id: 'service_test_001',
    name: 'Career Consultation',
    description: 'Professional career guidance',
    mentor: {
      id: 'mentor_test_001',
      name: 'Test Mentor',
      email: '<EMAIL>',
    },
  },
  package: null,
  immigration_service: null,
  training: null,
};

export const mockUpdatedPayment = {
  ...mockUnifiedPayment,
  progress: Status.Active,
  updatedAt: new Date('2024-01-02'),
};

export const mockBulkUpdateResult = {
  count: 2,
};

// ========================================
// MOCK ADMIN RESPONSE DATA
// ========================================

export const mockAdminProgressResponse: AdminPaymentResponseDto = {
  success: true,
  message: 'Payment progress updated successfully',
  payment: mockUpdatedPayment,
};

export const mockAdminStatusResponse: AdminPaymentResponseDto = {
  success: true,
  message: 'Payment status updated successfully',
  payment: {
    ...mockUpdatedPayment,
    status: 'paid',
  },
};

export const mockBulkUpdateResponse: AdminPaymentResponseDto = {
  success: true,
  message: 'Successfully updated 2 payments',
  updatedCount: 2,
};

export const mockDatabaseError = new Error(errorMessages.databaseError);
export const mockNotFoundError = new Error(errorMessages.paymentNotFound);

// ========================================
// PRISMA MOCK RESPONSES
// ========================================

export const mockPrismaUpdateResponse = mockUpdatedPayment;
export const mockPrismaUpdateManyResponse = mockBulkUpdateResult;

export const mockPrismaNotFoundError = {
  code: 'P2025',
  message: 'Record not found',
};

// ========================================
// ENHANCED PAYMENT METADATA TEST DATA
// ========================================

/**
 * Mock Stripe PaymentIntent for testing payment metadata extraction
 */
export const mockStripePaymentIntent = {
  id: 'pi_test_123',
  object: 'payment_intent',
  amount: 10000,
  currency: 'eur',
  status: 'succeeded',
  payment_method: {
    id: 'pm_test_123',
    object: 'payment_method',
    type: 'card',
    card: {
      brand: 'visa',
      last4: '4242',
    },
  },
  latest_charge: {
    id: 'ch_test_123',
    object: 'charge',
    amount: 10000,
    currency: 'eur',
    status: 'succeeded',
  },
};

/**
 * Mock Stripe PaymentMethod for testing
 */
export const mockStripePaymentMethod = {
  id: 'pm_test_123',
  object: 'payment_method',
  type: 'card',
  card: {
    brand: 'visa',
    last4: '4242',
  },
};

/**
 * Mock Enhanced Payment Metadata
 */
export const mockPaymentMetadata = {
  paymentIntentId: 'pi_test_123',
  paymentMethod: 'card',
  transactionId: 'ch_test_123',
};

// ========================================
// HELPER FUNCTIONS
// ========================================

export function resetAllMocks() {
  jest.clearAllMocks();
}

export function createMockAdminProgressDto(
  overrides: Partial<AdminPaymentProgressDto> = {},
): AdminPaymentProgressDto {
  return {
    ...validAdminProgressDto,
    ...overrides,
  };
}

export function createMockAdminStatusDto(
  overrides: Partial<AdminPaymentStatusDto> = {},
): AdminPaymentStatusDto {
  return {
    ...validAdminStatusDto,
    ...overrides,
  };
}

export function createMockBulkUpdateDto(
  overrides: Partial<AdminBulkPaymentUpdateDto> = {},
): AdminBulkPaymentUpdateDto {
  return {
    ...validBulkUpdateDto,
    ...overrides,
  };
}

export function createMockPayment(
  overrides: Partial<typeof mockUnifiedPayment> = {},
) {
  return {
    ...mockUnifiedPayment,
    ...overrides,
  };
}

// ========================================
// TEST SCENARIOS
// ========================================

export const testScenarios = {
  progressUpdate: {
    valid: {
      dto: validAdminProgressDto,
      expectedResponse: mockAdminProgressResponse,
    },
    invalid: {
      dto: invalidAdminProgressDto,
      expectedError: errorMessages.invalidPaymentId,
    },
    notFound: {
      dto: createMockAdminProgressDto({ paymentId: 'payment_invalid_001' }),
      expectedError: errorMessages.paymentNotFound,
    },
  },
  statusUpdate: {
    valid: {
      dto: validAdminStatusDto,
      expectedResponse: mockAdminStatusResponse,
    },
    invalid: {
      dto: invalidAdminStatusDto,
      expectedError: errorMessages.invalidStatus,
    },
  },
  bulkUpdate: {
    valid: {
      dto: validBulkUpdateDto,
      expectedResponse: mockBulkUpdateResponse,
    },
    invalid: {
      dto: invalidBulkUpdateDto,
      expectedError: errorMessages.bulkUpdateFailed,
    },
  },
};

// ========================================
// PERFORMANCE TEST DATA
// ========================================

export const performanceTestData = {
  singleUpdate: {
    expectedMaxTime: 100, // milliseconds
    description: 'Single payment update should complete within 100ms',
  },
  bulkUpdate: {
    expectedMaxTime: 500, // milliseconds
    description: 'Bulk update of 100 payments should complete within 500ms',
  },
  largeDataset: {
    paymentCount: 1000,
    batchSize: 100,
    description: 'Performance test with 1000 payments in batches of 100',
  },
};
