import { Modu<PERSON> } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { DashboardService } from './dashboard.service';
import { DashboardRefactoredService } from './dashboard-refactored.service';

@Module({
  controllers: [DashboardController],
  providers: [
    PrismaService,
    JwtService,
    DashboardService,
    DashboardRefactoredService,
  ],
})
export class DashboardModule {}
