import {
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { LoginDto, MentorDto } from './dto/mentor.dto';
import { compare } from 'bcrypt';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from 'src/utils/prisma.service';
import { hash } from 'bcrypt';
import { VerifyOtpDto } from 'src/otp/dto/otp.dto';
import { OtpService } from 'src/otp/otp.service';

const EXPIRE_TIME = 20 * 1000;

@Injectable()
export class MentorService {
  constructor(
    private jwtService: JwtService,
    private prisma: PrismaService,
    private otpService: OtpService,
  ) {}

  async login(dto: LoginDto) {
    const user = await this.validateUser(dto);
    const payload = {
      id: user.id,
      email: user.email,
      sub: {
        name: user.name,
      },
    };
    if (!user.emailVerified) {
      await this.otpService.generateOTPToken({
        email: user.email,
        url: process.env.VERIFY_CLIENT_EMAIL_URL,
      });
      throw new UnauthorizedException('Your Email is not verified');
    }
    return {
      user,
      backendTokens: {
        accessToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtMentorSecretKey,
        }),
        refreshToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtMentorSecretKey,
        }),
        expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
      },
    };
  }

  async otherLogin(dto: MentorDto) {
    const user = await this.prisma.mentor.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user ?? { password: null };
      const payload = {
        id: user.id,
        email: dto.email,
        sub: {
          name: dto.name,
        },
      };
      return {
        user: result,
        backendTokens: {
          accessToken: await this.jwtService.signAsync(payload, {
            expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
            secret: process.env.jwtMentorSecretKey,
          }),
          refreshToken: await this.jwtService.signAsync(payload, {
            expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
            secret: process.env.jwtRefreshTokenKey,
          }),
          expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
        },
      };
    }

    const newUser = await this.prisma.mentor.create({
      data: {
        ...dto,
      },
    });
    if (!newUser) throw new ConflictException();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...newResult } = newUser;
    const payload = {
      id: newUser.id,
      email: dto.email,
      sub: {
        name: dto.name,
      },
    };
    return {
      user: newResult,
      backendTokens: {
        accessToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtMentorSecretKey,
        }),
        refreshToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtRefreshTokenKey,
        }),
        expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
      },
    };
  }
  async register(dto: MentorDto) {
    const user = await this.prisma.mentor.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) throw new ConflictException('Email duplicated');

    const newUser = await this.prisma.mentor.create({
      data: {
        ...dto,
        password: await hash(dto.password, 10),
      },
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = newUser;
    const otp = await this.otpService.generateOTPToken({
      email: result.email,
      url: process.env.VERIFY_CLIENT_EMAIL_URL,
    });
    return {
      token: otp.token,
      status: 'Ok',
      message: 'Verify your email address',
    };
  }
  async adminRegister(dto: MentorDto) {
    const user = await this.prisma.mentor.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) throw new ConflictException('Email duplicated');

    const newUser = await this.prisma.mentor.create({
      data: {
        ...dto,
        status: 'Accepted',
        password: await hash(dto.password, 10),
        emailVerified: true,
      },
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = newUser;
    return result;
  }

  async adminUpdateMentor(id: string, dto: MentorDto) {
    let mentor;
    if (dto?.password) {
      mentor = await this.prisma.mentor.update({
        where: {
          id: id,
        },
        data: {
          ...dto,
          password: await hash(dto.password, 10),
        },
      });
    } else {
      mentor = await this.prisma.mentor.update({
        where: {
          id: id,
        },
        data: {
          ...dto,
        },
      });
    }

    return mentor;
  }

  async profile(req: Request) {
    const mentor: string = req['id'];
    const user = await this.prisma.mentor.findUnique({
      where: {
        id: mentor,
      },
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = user;

    return result;
  }

  async validateUser(dto: LoginDto) {
    const user = await this.prisma.mentor.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user && (await compare(dto.password, user.password))) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user;
      return result;
    }
    throw new UnauthorizedException('Wrong email or password');
  }

  async refreshToken(user: any) {
    const payload = {
      id: user.id,
      email: user.email,
      sub: user.sub,
    };

    return {
      accessToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtMentorSecretKey,
      }),
      refreshToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtRefreshTokenKey,
      }),
      expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
    };
  }

  async verifyEmail(dto: VerifyOtpDto) {
    const verify = await this.otpService.verifyOTPToken(dto);
    const user = await this.prisma.mentor.update({
      where: {
        email: verify.email,
      },
      data: {
        emailVerified: true,
      },
    });
    if (!user) throw new UnauthorizedException('user is not registered');

    return { status: 'OK', message: 'User Email Verified' };
  }

  async getMentor(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;

    const mentors = await this.prisma.mentor.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        createdAt: true,
        designation: true,
        desc: true,
        order: true,
        emailVerified: true,
        updatedAt: true,
      },

      orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
    });

    return mentors;
  }
  async getMentorByIdForAdmin(id: string) {
    const mentor = await this.prisma.mentor.findUnique({
      where: {
        id: id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        designation: true,
        desc: true,
        createdAt: true,
        order: true,
        location: true,
        status: true,
        linkedin: true,
        profile: true,
        updatedAt: true,
        password: false,
        reviews: {
          where: {
            userId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            rating: true,
            message: true,
            createdAt: true,
            updatedAt: true,
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        services: {
          select: {
            id: true,
            createdAt: true,
            description: true,
            meeting_link: true,
            name: true,
            price: true,
            updatedAt: true,
            status: true,
            users: {
              where: {
                userId: { not: null }, // Exclude reviews where userId is NULL
              },
              select: {
                id: true,
                amount: true,
                status: true,
                progress: true,
                createdAt: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                  },
                },
              },
            },
            guest: {
              select: {
                id: true,
                amount: true,
                status: true,
                name: true,
                email: true,
                mobile_no: true,
                progress: true,
                createdAt: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    const total_revenue = await this.prisma.$queryRaw<
      { total_revenue: number | null }[]
    >`
      SELECT 
        COALESCE(SUM(total_amount), 0) AS total_revenue
      FROM (
        SELECT 
          ums.amount AS total_amount
        FROM 
          service s
        INNER JOIN 
          user_mentor_service ums 
        ON 
          s.id = ums."serviceId"
        WHERE 
          s."mentorId" = ${id} AND ums.status = 'paid'
        
        UNION ALL
        
        SELECT 
          gms.amount AS total_amount
        FROM 
          service s
        INNER JOIN 
          guest_mentor_service gms 
        ON 
          s.id = gms."serviceId"
        WHERE 
          s."mentorId" = ${id} AND gms.status = 'paid'
      ) AS combined_revenue
  `;

    return {
      ...mentor,
      total_revenue: total_revenue[0]?.total_revenue
        ? total_revenue[0]?.total_revenue.toString()
        : '0',
    };
  }
  async getMentorById(id: string) {
    const mentor = await this.prisma.mentor.findUnique({
      where: {
        id: id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        designation: true,
        desc: true,
        createdAt: true,
        order: true,
        location: true,
        linkedin: true,
        profile: true,
        status: true,
        updatedAt: true,
        services: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    return mentor;
  }
  async getMentorByName(name: string) {
    const mentor = await this.prisma.mentor.findFirst({
      where: {
        name: decodeURI(name),
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        designation: true,
        desc: true,
        order: true,
        createdAt: true,
        location: true,
        linkedin: true,
        profile: true,
        status: true,
        updatedAt: true,
        services: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    return mentor;
  }

  async adminRemove(id: string) {
    const data = await this.prisma.mentor.delete({
      where: {
        id,
      },
    });

    return data;
  }
}
