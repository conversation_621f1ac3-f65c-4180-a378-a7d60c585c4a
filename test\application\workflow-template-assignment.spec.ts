/**
 * Workflow Template Assignment Tests
 *
 * Comprehensive tests for the assignWorkflowTemplate functionality
 * including error scenarios, edge cases, and atomic transaction behavior.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationService } from '../../src/application/application.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { ApplicationFormService } from '../../src/application/services/application-form.service';
import { ApplicationDocumentService } from '../../src/application/services/application-document.service';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';
import { MediaService } from '../../src/media/media.service';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { AssignWorkflowTemplateDto } from '../../src/application/dto/application.dto';

describe('ApplicationService - Workflow Template Assignment', () => {
  let service: ApplicationService;
  const mockPrismaService = {
    application: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    workflow_template: {
      findUnique: jest.fn(),
    },
    application_form: {
      findMany: jest.fn(),
      deleteMany: jest.fn(),
      createMany: jest.fn(),
    },
    application_document: {
      findMany: jest.fn(),
      deleteMany: jest.fn(),
      createMany: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  const mockMediaService = {
    deleteMultipleFiles: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  };

  const mockFormService = {};
  const mockDocumentService = {};
  const mockTransformerService = {};
  const mockDocumentVaultService = {};
  const mockNotificationService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        { provide: PrismaService, useValue: mockPrismaService },
        { provide: LoggerService, useValue: mockLoggerService },
        { provide: ApplicationFormService, useValue: mockFormService },
        { provide: ApplicationDocumentService, useValue: mockDocumentService },
        {
          provide: ApplicationTransformerService,
          useValue: mockTransformerService,
        },
        { provide: MediaService, useValue: mockMediaService },
        { provide: DocumentVaultService, useValue: mockDocumentVaultService },
        { provide: NotificationService, useValue: mockNotificationService },
      ],
    }).compile();

    service = module.get<ApplicationService>(ApplicationService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('assignWorkflowTemplate', () => {
    const validDto: AssignWorkflowTemplateDto = {
      application_id: 'app-123',
      new_workflow_template_id: 'template-456',
    };

    const mockApplication = {
      id: 'app-123',
      workflow_template_id: 'old-template-789',
      service_type: 'immigration',
      service_id: 'service-001',
    };

    const mockWorkflowTemplate = {
      id: 'template-456',
      name: 'New Immigration Template',
      isActive: true,
      serviceType: 'immigration',
      serviceId: null,
      workflowTemplate: [
        {
          stageOrder: 1,
          stageName: 'Personal Details',
          customFormRequired: true,
          customForm: [
            {
              fieldName: 'full_name',
              fieldType: 'text',
              required: true,
              showToClient: true,
            },
          ],
          documentsRequired: true,
          documents: [
            {
              documentName: 'passport',
              required: true,
              description: 'Valid passport',
            },
          ],
        },
      ],
    };

    it('should successfully assign workflow template', async () => {
      // Setup mocks
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );

      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockTx = {
          application_form: {
            findMany: jest.fn().mockResolvedValue([{ id: 'form-1' }]),
            deleteMany: jest.fn().mockResolvedValue({ count: 1 }),
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
          application_document: {
            findMany: jest
              .fn()
              .mockResolvedValue([
                {
                  id: 'doc-1',
                  document: { file_path: 'documents/app-123/file1.pdf' },
                },
              ]),
            deleteMany: jest.fn().mockResolvedValue({ count: 1 }),
            create: jest.fn().mockResolvedValue({ id: 'app-doc-1' }),
          },
          document_vault: {
            create: jest.fn().mockResolvedValue({ id: 'vault-doc-1' }),
          },
          application: {
            update: jest.fn().mockResolvedValue(mockApplication),
          },
        };
        return await callback(mockTx);
      });

      mockMediaService.deleteMultipleFiles.mockResolvedValue({
        success: ['documents/app-123/file1.pdf'],
        failed: [],
      });

      // Execute
      const result = await service.assignWorkflowTemplate(validDto);

      // Assertions
      expect(result).toEqual({
        success: true,
        message: 'Workflow template assigned successfully',
        application_id: 'app-123',
        workflow_template_id: 'template-456',
        form_records_cleaned: 1,
        document_records_cleaned: 1,
        storage_files_cleaned: 1,
      });

      expect(mockPrismaService.application.findUnique).toHaveBeenCalledWith({
        where: { id: 'app-123' },
        select: {
          id: true,
          workflow_template_id: true,
          service_type: true,
          service_id: true,
        },
      });

      expect(
        mockPrismaService.workflow_template.findUnique,
      ).toHaveBeenCalledWith({
        where: { id: 'template-456' },
        select: {
          id: true,
          name: true,
          isActive: true,
          serviceType: true,
          serviceId: true,
          workflowTemplate: true,
        },
      });

      expect(mockPrismaService.$transaction).toHaveBeenCalled();
      expect(mockMediaService.deleteMultipleFiles).toHaveBeenCalled();
    });

    it('should throw NotFoundException when application does not exist', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(null);

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new NotFoundException('Application with ID app-123 not found'),
      );
    });

    it('should throw NotFoundException when workflow template does not exist', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(null);

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new NotFoundException(
          'Workflow template with ID template-456 not found',
        ),
      );
    });

    it('should throw BadRequestException when workflow template is not active', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        ...mockWorkflowTemplate,
        isActive: false,
      });

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new BadRequestException('Workflow template template-456 is not active'),
      );
    });

    it('should throw BadRequestException when service types do not match', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        ...mockWorkflowTemplate,
        serviceType: 'training',
      });

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new BadRequestException(
          'Workflow template service type (training) does not match application service type (immigration)',
        ),
      );
    });

    it('should throw BadRequestException when specific service IDs do not match', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        ...mockWorkflowTemplate,
        serviceId: 'different-service-id',
      });

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new BadRequestException(
          'Workflow template is specific to service ID different-service-id but application uses service ID service-001',
        ),
      );
    });

    it('should throw BadRequestException when application already uses the workflow template', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue({
        ...mockApplication,
        workflow_template_id: 'template-456',
      });
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new BadRequestException(
          'Application already uses workflow template template-456',
        ),
      );
    });

    it('should handle storage cleanup failures gracefully', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );

      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockTx = {
          application_form: {
            findMany: jest.fn().mockResolvedValue([{ id: 'form-1' }]),
            deleteMany: jest.fn().mockResolvedValue({ count: 1 }),
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
          application_document: {
            findMany: jest
              .fn()
              .mockResolvedValue([
                {
                  id: 'doc-1',
                  document: { file_path: 'documents/app-123/file1.pdf' },
                },
              ]),
            deleteMany: jest.fn().mockResolvedValue({ count: 1 }),
            create: jest.fn().mockResolvedValue({ id: 'app-doc-1' }),
          },
          document_vault: {
            create: jest.fn().mockResolvedValue({ id: 'vault-doc-1' }),
          },
          application: {
            update: jest.fn().mockResolvedValue(mockApplication),
          },
        };
        return await callback(mockTx);
      });

      // Simulate storage cleanup failure
      mockMediaService.deleteMultipleFiles.mockRejectedValue(
        new Error('Storage service unavailable'),
      );

      const result = await service.assignWorkflowTemplate(validDto);

      // Should still succeed even if storage cleanup fails
      expect(result.success).toBe(true);
      expect(result.storage_files_cleaned).toBe(0);
      expect(result.form_records_cleaned).toBe(1);
      expect(result.document_records_cleaned).toBe(1);

      // Verify that storage cleanup was attempted
      expect(mockMediaService.deleteMultipleFiles).toHaveBeenCalledWith([
        'documents/app-123/file1.pdf',
      ]);
    });

    it('should handle document_vault creation and prevent unique constraint errors', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );

      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        const mockTx = {
          application_form: {
            findMany: jest.fn().mockResolvedValue([]),
            deleteMany: jest.fn().mockResolvedValue({ count: 0 }),
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
          application_document: {
            findMany: jest.fn().mockResolvedValue([]),
            deleteMany: jest.fn().mockResolvedValue({ count: 0 }),
            create: jest.fn().mockResolvedValue({ id: 'app-doc-1' }),
          },
          document_vault: {
            create: jest.fn().mockResolvedValue({ id: 'vault-doc-1' }),
          },
          application: {
            update: jest.fn().mockResolvedValue(mockApplication),
          },
        };
        return await callback(mockTx);
      });

      mockMediaService.deleteMultipleFiles.mockResolvedValue({
        success: [],
        failed: [],
      });

      const result = await service.assignWorkflowTemplate(validDto);

      expect(result.success).toBe(true);
      expect(mockPrismaService.$transaction).toHaveBeenCalled();
    });

    it('should throw BadRequestException for invalid workflow template structure', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        ...mockWorkflowTemplate,
        workflowTemplate: null, // Invalid structure
      });

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new BadRequestException(
          'Invalid workflow template structure: template must contain an array of stages',
        ),
      );
    });

    it('should throw BadRequestException for empty workflow template', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue({
        ...mockWorkflowTemplate,
        workflowTemplate: [], // Empty array
      });

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new BadRequestException(
          'Invalid workflow template: template must contain at least one stage',
        ),
      );
    });

    it('should handle transaction rollback on database error', async () => {
      mockPrismaService.application.findUnique.mockResolvedValue(
        mockApplication,
      );
      mockPrismaService.workflow_template.findUnique.mockResolvedValue(
        mockWorkflowTemplate,
      );

      // Simulate transaction failure
      mockPrismaService.$transaction.mockRejectedValue(
        new Error('Database connection lost'),
      );

      await expect(service.assignWorkflowTemplate(validDto)).rejects.toThrow(
        new BadRequestException(
          'Failed to assign workflow template: Database connection lost',
        ),
      );
    });
  });
});
