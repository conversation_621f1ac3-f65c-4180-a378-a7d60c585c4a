/**
 * Unified Payment Service Tests
 *
 * Comprehensive test suite for UnifiedPaymentService.
 * Tests all business logic, database operations, Stripe integration, and error handling.
 *
 * @desc Tests the unified payment service that consolidates all payment operations
 * @assumptions Uses mocked dependencies (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mailer)
 * @mocked_dependencies Stripe, PrismaService, MailerService
 */

// Mock external dependencies
jest.mock('@react-email/components', () => ({
  render: jest.fn().mockReturnValue('<html>Mock Email</html>'),
}));

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UnifiedPaymentService } from '../../src/payment/unified-payment.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { LoggerService } from '../../src/utils/logger.service';
import { ApplicationIntegrationService } from '../../src/application/services/application-integration.service';
import { STRIPE_CLIENT } from '../../src/config/stripe.config';
import {
  mockJWTPayload,
  mockStripeClient,
  mockPrismaService,
  mockMailerService,
  mockLoggerService,
  mockStripeSession,
  mockPaymentRecord,
  mockServiceData,
  mockServiceDataWithMentorRelation,
  mockPackageData,
  mockImmigrationServiceData,
  mockTrainingData,
  IJWTPayload,
} from '../utils/test-helpers';
import {
  validUserPaymentDto,
  validGuestPaymentDto,
  validPackagePaymentDto,
  validImmigrationPaymentDto,
  validTrainingPaymentDto,
  invalidPaymentDtos,
  validPaymentFilters,
  emptyPaymentFilters,
  paginationFilters,
  largePaginationFilters,
  mockPaginatedPaymentHistory,
  mockPaymentAnalytics,
  mockPaymentHistory,
  mockWebhookEvent,
  errorMessages,
  validAdminProgressDto,
  validAdminStatusDto,
  validBulkUpdateDto,
  invalidAdminProgressDto,
  invalidAdminStatusDto,
  invalidBulkUpdateDto,
  mockUnifiedPayment,
  mockUpdatedPayment,
  mockBulkUpdateResult,
  mockPrismaNotFoundError,
  resetAllMocks,
} from '../fixtures/payment-fixtures';
import { PaymentType, ServiceType } from '../../src/payment/dto/payment.dto';

describe('UnifiedPaymentService', () => {
  let service: UnifiedPaymentService;
  let prisma: PrismaService;
  let mailer: MailerService;
  let stripe: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UnifiedPaymentService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: STRIPE_CLIENT,
          useValue: mockStripeClient,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: ApplicationIntegrationService,
          useValue: {
            createApplicationFromPayment: jest.fn(),
            validateWorkflowTemplate: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UnifiedPaymentService>(UnifiedPaymentService);
    prisma = module.get<PrismaService>(PrismaService);
    mailer = module.get<MailerService>(MailerService);
    stripe = module.get(STRIPE_CLIENT);

    // Reset all mocks before each test
    resetAllMocks();
  });

  afterEach(() => {
    resetAllMocks();
  });

  describe('createPayment', () => {
    describe('User Payments', () => {
      it('should create payment for authenticated user with service', async () => {
        // Arrange
        const mockPendingPayment = { id: 'pending_payment_123' };
        mockPrismaService.service.findUnique.mockResolvedValue(
          mockServiceDataWithMentorRelation,
        );
        // Mock user validation
        mockPrismaService.user.findUnique.mockResolvedValue({
          id: mockJWTPayload.id,
        });
        // No intelligent session validation in current implementation
        mockPrismaService.payment.create.mockResolvedValue(mockPendingPayment);
        mockPrismaService.payment.update.mockResolvedValue(mockPendingPayment);
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createPayment(
          mockJWTPayload,
          validUserPaymentDto,
        );

        // Assert
        expect(result).toEqual({
          status: 'OK',
          url: mockStripeSession.url,
          paymentId: mockPendingPayment.id,
        });

        // Verify payment data is stored before Stripe session creation
        expect(prisma.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            amount: mockServiceData.amount,
            status: 'pending',
            payment_type: PaymentType.USER,
            service_type: ServiceType.SERVICE,
            progress: 'Pending',
            userId: mockJWTPayload.id,
            serviceId: validUserPaymentDto.serviceId,
          }),
        });

        // Verify Stripe session is created
        expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
          expect.objectContaining({
            line_items: expect.arrayContaining([
              expect.objectContaining({
                price_data: expect.objectContaining({
                  currency: 'eur',
                  product_data: expect.objectContaining({
                    name: mockServiceData.name,
                    description: mockServiceData.description,
                  }),
                  unit_amount: mockServiceData.amount * 100,
                }),
                quantity: 1,
              }),
            ]),
            metadata: expect.objectContaining({
              serviceType: ServiceType.SERVICE,
              serviceId: validUserPaymentDto.serviceId,
              paymentType: PaymentType.USER,
              userId: mockJWTPayload.id,
              amount: mockServiceData.amount.toString(),
            }),
            mode: 'payment',
          }),
        );

        // Verify payment is updated with Stripe session ID
        expect(prisma.payment.update).toHaveBeenCalledWith({
          where: { id: mockPendingPayment.id },
          data: {
            stripe_session_id: mockStripeSession.id,
            updatedAt: expect.any(Date),
          },
        });
      });

      it('should create payment for package service', async () => {
        // Arrange
        mockPrismaService.packages.findUnique.mockResolvedValue(
          mockPackageData,
        );
        // Mock user validation
        mockPrismaService.user.findUnique.mockResolvedValue({
          id: mockJWTPayload.id,
        });
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createPayment(
          mockJWTPayload,
          validPackagePaymentDto,
        );

        // Assert
        expect(result.status).toBe('OK');
        expect(prisma.packages.findUnique).toHaveBeenCalledWith({
          where: { id: validPackagePaymentDto.serviceId },
        });
        expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              serviceType: ServiceType.PACKAGE,
            }),
          }),
        );
      });

      it('should create payment for immigration service', async () => {
        // Arrange
        mockPrismaService.immigration_service.findUnique.mockResolvedValue(
          mockImmigrationServiceData,
        );
        // Mock user validation
        mockPrismaService.user.findUnique.mockResolvedValue({
          id: mockJWTPayload.id,
        });
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createPayment(
          mockJWTPayload,
          validImmigrationPaymentDto,
        );

        // Assert
        expect(result.status).toBe('OK');
        expect(prisma.immigration_service.findUnique).toHaveBeenCalledWith({
          where: { id: validImmigrationPaymentDto.serviceId },
        });
        expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              serviceType: ServiceType.IMMIGRATION,
            }),
          }),
        );
      });

      it('should create payment for training service', async () => {
        // Arrange
        mockPrismaService.training.findUnique.mockResolvedValue(
          mockTrainingData,
        );
        // Mock user validation
        mockPrismaService.user.findUnique.mockResolvedValue({
          id: mockJWTPayload.id,
        });
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createPayment(
          mockJWTPayload,
          validTrainingPaymentDto,
        );

        // Assert
        expect(result.status).toBe('OK');
        expect(prisma.training.findUnique).toHaveBeenCalledWith({
          where: { id: validTrainingPaymentDto.serviceId },
        });
        expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              serviceType: ServiceType.TRAINING,
            }),
          }),
        );
      });

      it('should throw NotFoundException when service not found', async () => {
        // Arrange
        mockPrismaService.service.findUnique.mockResolvedValue(null);

        // Act & Assert
        await expect(
          service.createPayment(mockJWTPayload, validUserPaymentDto),
        ).rejects.toThrow(BadRequestException);
        expect(prisma.service.findUnique).toHaveBeenCalledWith({
          where: { id: validUserPaymentDto.serviceId },
          include: {
            mentor: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        });
        expect(stripe.checkout.sessions.create).not.toHaveBeenCalled();
      });

      it('should throw BadRequestException when user not found in database', async () => {
        // Arrange
        mockPrismaService.service.findUnique.mockResolvedValue(
          mockServiceDataWithMentorRelation,
        );
        // Mock user validation to return null (user not found)
        mockPrismaService.user.findUnique.mockResolvedValue(null);

        // Act & Assert
        await expect(
          service.createPayment(mockJWTPayload, validUserPaymentDto),
        ).rejects.toThrow(BadRequestException);

        // Verify user validation was called
        expect(prisma.user.findUnique).toHaveBeenCalledWith({
          where: { id: mockJWTPayload.id },
          select: { id: true },
        });

        // Verify payment creation was not attempted
        expect(prisma.payment.create).not.toHaveBeenCalled();
        expect(stripe.checkout.sessions.create).not.toHaveBeenCalled();
      });

      it('should handle Stripe session creation failure', async () => {
        // Arrange
        mockPrismaService.service.findUnique.mockResolvedValue(
          mockServiceDataWithMentorRelation,
        );
        // Mock user validation
        mockPrismaService.user.findUnique.mockResolvedValue({
          id: mockJWTPayload.id,
        });
        mockStripeClient.checkout.sessions.create.mockRejectedValue(
          new Error('Stripe API error'),
        );

        // Act & Assert
        await expect(
          service.createPayment(mockJWTPayload, validUserPaymentDto),
        ).rejects.toThrow('Stripe API error');
      });
    });

    describe('Guest Payments', () => {
      it('should create payment for guest user with complete information', async () => {
        // Arrange
        mockPrismaService.service.findUnique.mockResolvedValue(
          mockServiceDataWithMentorRelation,
        );
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createPayment(null, validGuestPaymentDto);

        // Assert
        expect(result.status).toBe('OK');
        expect(stripe.checkout.sessions.create).toHaveBeenCalledWith(
          expect.objectContaining({
            metadata: expect.objectContaining({
              paymentType: PaymentType.GUEST,
              guestName: validGuestPaymentDto.name,
              guestEmail: validGuestPaymentDto.email,
              guestMobile: validGuestPaymentDto.mobile,
            }),
          }),
        );
        expect(stripe.checkout.sessions.create).toHaveBeenCalled();
      });

      it('should throw BadRequestException for guest payment without guest info', async () => {
        // Arrange
        const invalidGuestDto = {
          ...validGuestPaymentDto,
          name: undefined,
          email: undefined,
          mobile: undefined,
        };

        // Act & Assert
        await expect(
          service.createPayment(null, invalidGuestDto),
        ).rejects.toThrow(BadRequestException);
        expect(prisma.service.findUnique).not.toHaveBeenCalled();
      });
    });

    describe('Validation', () => {
      it('should throw BadRequestException for user payment without user', async () => {
        // Act & Assert
        await expect(
          service.createPayment(null, validUserPaymentDto),
        ).rejects.toThrow(BadRequestException);
      });

      it('should throw BadRequestException for guest payment with user', async () => {
        // Act & Assert
        await expect(
          service.createPayment(mockJWTPayload, {
            ...validGuestPaymentDto,
            paymentType: PaymentType.GUEST,
          }),
        ).rejects.toThrow(BadRequestException);
      });
    });
  });

  describe('processWebhook', () => {
    it('should update existing payment record on successful webhook', async () => {
      // Arrange
      const mockReq = {
        headers: { 'stripe-signature': 'test_signature' },
        rawBody: JSON.stringify(mockWebhookEvent),
      };
      const existingPayment = { id: 'existing_payment_123' };
      const updatedPayment = { ...existingPayment, status: 'paid' };

      mockStripeClient.webhooks.constructEvent.mockReturnValue(
        mockWebhookEvent,
      );
      mockPrismaService.payment.findFirst.mockResolvedValue(existingPayment);
      mockPrismaService.payment.update.mockResolvedValue(updatedPayment);
      mockMailerService.sendEmail.mockResolvedValue(true);

      // Mock the extractPaymentMetadata method
      jest.spyOn(service as any, 'extractPaymentMetadata').mockResolvedValue({
        paymentIntentId: 'pi_test_123',
        paymentMethod: 'card',
        transactionId: null,
      });

      // Act
      const result = await service.processWebhook(mockReq);

      // Assert
      expect(result).toEqual({ received: true });
      expect(stripe.webhooks.constructEvent).toHaveBeenCalledWith(
        mockReq.rawBody,
        mockReq.headers['stripe-signature'],
        process.env.STRIPE_WEBHOOK_SECRET,
      );

      // Verify it looks for existing payment by payment ID from metadata
      expect(prisma.payment.findFirst).toHaveBeenCalledWith({
        where: {
          id: mockWebhookEvent.data.object.metadata.paymentId,
        },
      });

      // Verify it updates the existing payment instead of creating new one
      expect(prisma.payment.update).toHaveBeenCalledWith({
        where: { id: existingPayment.id },
        data: {
          status: 'paid',
          stripe_payment_intent_id: 'pi_test_123',
          payment_method: 'card',
          transaction_id: null,
          updatedAt: expect.any(Date),
        },
      });

      expect(prisma.payment.create).not.toHaveBeenCalled();
    });

    it('should create new payment record when no existing payment found (fallback)', async () => {
      // Arrange
      const mockReq = {
        headers: { 'stripe-signature': 'test_signature' },
        rawBody: JSON.stringify(mockWebhookEvent),
      };

      mockStripeClient.webhooks.constructEvent.mockReturnValue(
        mockWebhookEvent,
      );
      mockPrismaService.payment.findFirst.mockResolvedValue(null); // No existing payment
      mockPrismaService.payment.create.mockResolvedValue(mockPaymentRecord);
      mockMailerService.sendEmail.mockResolvedValue(true);

      // Act
      const result = await service.processWebhook(mockReq);

      // Assert
      expect(result).toEqual({ received: true });
      expect(prisma.payment.findFirst).toHaveBeenCalled();
      expect(prisma.payment.create).toHaveBeenCalled(); // Fallback to create
      expect(prisma.payment.update).not.toHaveBeenCalled();
    });

    it('should handle invalid webhook signature', async () => {
      // Arrange
      const mockReq = {
        headers: { 'stripe-signature': 'invalid_signature' },
        rawBody: JSON.stringify(mockWebhookEvent),
      };

      mockStripeClient.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      // Act & Assert
      await expect(service.processWebhook(mockReq)).rejects.toThrow(
        'Invalid signature',
      );
      expect(prisma.payment.create).not.toHaveBeenCalled();
    });

    it('should handle unsupported webhook event types', async () => {
      // Arrange
      const unsupportedEvent = {
        ...mockWebhookEvent,
        type: 'unsupported.event.type',
      };
      const mockReq = {
        headers: { 'stripe-signature': 'test_signature' },
        rawBody: JSON.stringify(unsupportedEvent),
      };

      mockStripeClient.webhooks.constructEvent.mockReturnValue(
        unsupportedEvent,
      );

      // Act
      const result = await service.processWebhook(mockReq);

      // Assert
      expect(result).toEqual({ received: true });
      expect(prisma.payment.create).not.toHaveBeenCalled();
    });

    it('should handle email notification failure gracefully', async () => {
      // Arrange
      const mockReq = {
        headers: { 'stripe-signature': 'test_signature' },
        rawBody: JSON.stringify(mockWebhookEvent),
      };

      mockStripeClient.webhooks.constructEvent.mockReturnValue(
        mockWebhookEvent,
      );
      mockPrismaService.payment.create.mockResolvedValue(mockPaymentRecord);
      mockMailerService.sendEmail.mockRejectedValue(
        new Error('Email service error'),
      );

      // Act
      const result = await service.processWebhook(mockReq);

      // Assert
      expect(result).toEqual({ received: true });
      expect(prisma.payment.create).toHaveBeenCalled();
      // Email failure should not prevent webhook processing success
    });
  });

  describe('getPaymentHistory', () => {
    it('should return paginated payment history with filters', async () => {
      // Arrange
      mockPrismaService.$transaction.mockResolvedValue([
        25,
        mockPaymentHistory,
      ]);

      // Act
      const result = await service.getPaymentHistory(validPaymentFilters);

      // Assert
      expect(result).toEqual({
        page: 1,
        limit: 10,
        totalPages: 3,
        totalItems: 25,
        data: mockPaymentHistory,
      });
      expect(prisma.$transaction).toHaveBeenCalledTimes(1);
    });

    it('should return paginated payment history without filters', async () => {
      // Arrange
      mockPrismaService.$transaction.mockResolvedValue([5, mockPaymentHistory]);

      // Act
      const result = await service.getPaymentHistory(emptyPaymentFilters);

      // Assert
      expect(result).toEqual({
        page: 1,
        limit: 10,
        totalPages: 1,
        totalItems: 5,
        data: mockPaymentHistory,
      });
      expect(prisma.$transaction).toHaveBeenCalledTimes(1);
    });

    it('should handle pagination with custom page and limit', async () => {
      // Arrange
      mockPrismaService.$transaction.mockResolvedValue([
        100,
        mockPaymentHistory,
      ]);

      // Act
      const result = await service.getPaymentHistory(largePaginationFilters);

      // Assert
      expect(result).toEqual({
        page: 2,
        limit: 50,
        totalPages: 2,
        totalItems: 100,
        data: mockPaymentHistory,
      });
      expect(prisma.$transaction).toHaveBeenCalledTimes(1);
    });

    it('should enforce pagination limits (max 100 per page)', async () => {
      // Arrange
      const invalidFilters = { page: 1, limit: 150 }; // Over limit
      mockPrismaService.$transaction.mockResolvedValue([
        10,
        mockPaymentHistory,
      ]);

      // Act
      const result = await service.getPaymentHistory(invalidFilters);

      // Assert
      expect(result.limit).toBe(100); // Should be capped at 100
      expect(prisma.$transaction).toHaveBeenCalledTimes(1);
    });

    it('should enforce minimum page number (1)', async () => {
      // Arrange
      const invalidFilters = { page: 0, limit: 10 }; // Invalid page
      mockPrismaService.$transaction.mockResolvedValue([
        10,
        mockPaymentHistory,
      ]);

      // Act
      const result = await service.getPaymentHistory(invalidFilters);

      // Assert
      expect(result.page).toBe(1); // Should be corrected to 1
      expect(prisma.$transaction).toHaveBeenCalledTimes(1);
    });

    // Temporarily disabled due to mock setup issue
    // it('should handle database error in payment history retrieval', async () => {
    //   // Arrange
    //   mockPrismaService.payment.count.mockRejectedValue(
    //     new Error('Database connection error'),
    //   );

    //   // Act & Assert
    //   await expect(
    //     service.getPaymentHistory(validPaymentFilters),
    //   ).rejects.toThrow('Database connection error');
    // });

    it('should return empty paginated result when no payments found', async () => {
      // Arrange
      mockPrismaService.$transaction.mockResolvedValue([0, []]);

      // Act
      const result = await service.getPaymentHistory(validPaymentFilters);

      // Assert
      expect(result).toEqual({
        page: 1,
        limit: 10,
        totalPages: 0,
        totalItems: 0,
        data: [],
      });
      expect(prisma.$transaction).toHaveBeenCalledTimes(1);
    });
  });

  describe('getPaymentAnalytics', () => {
    it('should return payment analytics with aggregated data', async () => {
      // Arrange
      mockPrismaService.payment.aggregate.mockResolvedValue({
        _sum: { amount: mockPaymentAnalytics.totalRevenue },
        _count: mockPaymentAnalytics.totalPayments,
      });
      mockPrismaService.payment.groupBy
        .mockResolvedValueOnce(mockPaymentAnalytics.paymentsByType)
        .mockResolvedValueOnce(mockPaymentAnalytics.paymentsByService);
      mockPrismaService.payment.findMany.mockResolvedValue(
        mockPaymentAnalytics.recentPayments,
      );

      // Act
      const result = await service.getPaymentAnalytics();

      // Assert
      expect(result).toEqual(mockPaymentAnalytics);
      expect(prisma.payment.aggregate).toHaveBeenCalledWith({
        _sum: { amount: true },
        _count: true,
        where: { status: 'paid' },
      });
      expect(prisma.payment.groupBy).toHaveBeenCalledWith({
        by: ['payment_type'],
        _sum: { amount: true },
        _count: true,
        where: { status: 'paid' },
      });
      expect(prisma.payment.groupBy).toHaveBeenCalledWith({
        by: ['service_type'],
        _sum: { amount: true },
        _count: true,
        where: { status: 'paid' },
      });
      expect(prisma.payment.findMany).toHaveBeenCalledWith({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          user: { select: { name: true, email: true } },
        },
      });
    });

    it('should handle null aggregation results', async () => {
      // Arrange
      mockPrismaService.payment.aggregate.mockResolvedValue({
        _sum: { amount: null },
        _count: 0,
      });
      mockPrismaService.payment.groupBy
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);
      mockPrismaService.payment.findMany.mockResolvedValue([]);

      // Act
      const result = await service.getPaymentAnalytics();

      // Assert
      expect(result).toEqual({
        totalRevenue: 0,
        totalPayments: 0,
        paymentsByType: [],
        paymentsByService: [],
        recentPayments: [],
      });
    });

    it('should handle database error in analytics retrieval', async () => {
      // Arrange
      mockPrismaService.payment.aggregate.mockRejectedValue(
        new Error('Analytics query failed'),
      );

      // Act & Assert
      await expect(service.getPaymentAnalytics()).rejects.toThrow(
        'Analytics query failed',
      );
    });
  });

  describe('Private Methods', () => {
    describe('validatePaymentRequest', () => {
      it('should validate user payment with user context', () => {
        const userPaymentDto = {
          ...validUserPaymentDto,
          paymentType: PaymentType.USER,
        };

        expect(() => {
          (service as any).validatePaymentRequest(
            mockJWTPayload,
            userPaymentDto,
          );
        }).not.toThrow();
      });

      it('should validate guest payment without user context', () => {
        const guestPaymentDto = {
          ...validGuestPaymentDto,
          paymentType: PaymentType.GUEST,
        };

        expect(() => {
          (service as any).validatePaymentRequest(null, guestPaymentDto);
        }).not.toThrow();
      });

      it('should throw BadRequestException for user payment without user', () => {
        const userPaymentDto = {
          ...validUserPaymentDto,
          paymentType: PaymentType.USER,
        };

        expect(() => {
          (service as any).validatePaymentRequest(null, userPaymentDto);
        }).toThrow(BadRequestException);
      });

      it('should validate guest payment with user context (no validation error)', () => {
        const guestPaymentDto = {
          ...validGuestPaymentDto,
          paymentType: PaymentType.GUEST,
        };

        expect(() => {
          (service as any).validatePaymentRequest(
            mockJWTPayload,
            guestPaymentDto,
          );
        }).not.toThrow();
      });

      it('should throw BadRequestException for guest payment without email', () => {
        const invalidGuestDto = {
          ...validGuestPaymentDto,
          paymentType: PaymentType.GUEST,
          email: undefined,
        };

        expect(() => {
          (service as any).validatePaymentRequest(null, invalidGuestDto);
        }).toThrow(BadRequestException);
      });
    });

    describe('getServiceData', () => {
      beforeEach(() => {
        resetAllMocks();
      });

      it('should retrieve service data for SERVICE type with mentor relationship', async () => {
        mockPrismaService.service.findUnique.mockResolvedValue(
          mockServiceDataWithMentorRelation,
        );

        const result = await (service as any).getServiceData(
          ServiceType.SERVICE,
          'service-123',
        );

        expect(result).toEqual(mockServiceData);
        expect(mockPrismaService.service.findUnique).toHaveBeenCalledWith({
          where: { id: 'service-123' },
          include: {
            mentor: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        });
      });

      it('should retrieve service data for PACKAGE type', async () => {
        mockPrismaService.packages.findUnique.mockResolvedValue(
          mockPackageData,
        );

        const result = await (service as any).getServiceData(
          ServiceType.PACKAGE,
          'package-123',
        );

        expect(result).toEqual(mockPackageData);
        expect(mockPrismaService.packages.findUnique).toHaveBeenCalledWith({
          where: { id: 'package-123' },
        });
      });

      it('should retrieve service data for IMMIGRATION type', async () => {
        mockPrismaService.immigration_service.findUnique.mockResolvedValue(
          mockImmigrationServiceData,
        );

        const result = await (service as any).getServiceData(
          ServiceType.IMMIGRATION,
          'immigration-123',
        );

        expect(result).toEqual(mockImmigrationServiceData);
        expect(
          mockPrismaService.immigration_service.findUnique,
        ).toHaveBeenCalledWith({
          where: { id: 'immigration-123' },
        });
      });

      it('should retrieve service data for TRAINING type', async () => {
        mockPrismaService.training.findUnique.mockResolvedValue(
          mockTrainingData,
        );

        const result = await (service as any).getServiceData(
          ServiceType.TRAINING,
          'training-123',
        );

        expect(result).toEqual(mockTrainingData);
        expect(mockPrismaService.training.findUnique).toHaveBeenCalledWith({
          where: { id: 'training-123' },
        });
      });

      it('should throw NotFoundException when service not found', async () => {
        mockPrismaService.service.findUnique.mockResolvedValue(null);

        await expect(
          (service as any).getServiceData(ServiceType.SERVICE, 'non-existent'),
        ).rejects.toThrow(NotFoundException);
      });

      it('should extract mentor name from database for SERVICE type', async () => {
        const serviceWithMentor = {
          ...mockServiceDataWithMentorRelation,
          mentor: {
            id: 'mentor_123',
            name: 'John Mentor',
          },
        };
        mockPrismaService.service.findUnique.mockResolvedValue(
          serviceWithMentor,
        );

        const result = await (service as any).getServiceData(
          ServiceType.SERVICE,
          'service-123',
        );

        expect(result.mentor).toBe('John Mentor');
        expect(mockPrismaService.service.findUnique).toHaveBeenCalledWith({
          where: { id: 'service-123' },
          include: {
            mentor: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        });
      });

      it('should handle SERVICE type with no mentor assigned', async () => {
        const serviceWithoutMentor = {
          ...mockServiceDataWithMentorRelation,
          mentor: null,
        };
        mockPrismaService.service.findUnique.mockResolvedValue(
          serviceWithoutMentor,
        );

        const result = await (service as any).getServiceData(
          ServiceType.SERVICE,
          'service-123',
        );

        expect(result.mentor).toBeUndefined();
      });

      it('should not include mentor relationship for non-SERVICE types', async () => {
        mockPrismaService.packages.findUnique.mockResolvedValue(
          mockPackageData,
        );

        const result = await (service as any).getServiceData(
          ServiceType.PACKAGE,
          'package-123',
        );

        expect(result.mentor).toBeUndefined();
        expect(mockPrismaService.packages.findUnique).toHaveBeenCalledWith({
          where: { id: 'package-123' },
        });
        // Should NOT include mentor relationship for non-service types
        expect(mockPrismaService.packages.findUnique).not.toHaveBeenCalledWith(
          expect.objectContaining({
            include: expect.anything(),
          }),
        );
      });
    });

    describe('createStripeSession', () => {
      beforeEach(() => {
        resetAllMocks();
      });

      it('should create Stripe session with correct parameters for user payment', async () => {
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        const result = await (service as any).createStripeSession(
          mockServiceData,
          validUserPaymentDto,
          mockJWTPayload,
        );

        expect(result).toEqual(mockStripeSession);
        expect(mockStripeClient.checkout.sessions.create).toHaveBeenCalledWith({
          line_items: [
            {
              price_data: {
                currency: 'eur',
                product_data: {
                  name: mockServiceData.name,
                  description: mockServiceData.description,
                },
                unit_amount: mockServiceData.amount * 100,
              },
              quantity: 1,
            },
          ],
          metadata: expect.objectContaining({
            serviceType: validUserPaymentDto.serviceType,
            serviceId: validUserPaymentDto.serviceId,
            paymentType: validUserPaymentDto.paymentType,
            amount: mockServiceData.amount.toString(),
            userId: mockJWTPayload.id,
          }),
          mode: 'payment',
          success_url: mockServiceData.meeting_link,
          cancel_url: process.env.CANCELED_URL,
        });
      });

      it('should create Stripe session with correct parameters for guest payment', async () => {
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        const result = await (service as any).createStripeSession(
          mockServiceData,
          validGuestPaymentDto,
          null,
        );

        expect(result).toEqual(mockStripeSession);
        expect(mockStripeClient.checkout.sessions.create).toHaveBeenCalledWith({
          line_items: [
            {
              price_data: {
                currency: 'eur',
                product_data: {
                  name: mockServiceData.name,
                  description: mockServiceData.description,
                },
                unit_amount: mockServiceData.amount * 100,
              },
              quantity: 1,
            },
          ],
          metadata: expect.objectContaining({
            serviceType: validGuestPaymentDto.serviceType,
            serviceId: validGuestPaymentDto.serviceId,
            paymentType: validGuestPaymentDto.paymentType,
            amount: mockServiceData.amount.toString(),
            guestName: validGuestPaymentDto.name,
            guestEmail: validGuestPaymentDto.email,
            guestMobile: validGuestPaymentDto.mobile,
          }),
          mode: 'payment',
          success_url: mockServiceData.meeting_link,
          cancel_url: process.env.CANCELED_URL,
        });
      });

      it('should handle Stripe session creation failure', async () => {
        mockStripeClient.checkout.sessions.create.mockRejectedValue(
          new Error('Stripe API error'),
        );

        await expect(
          (service as any).createStripeSession(
            mockServiceData,
            validUserPaymentDto,
            mockJWTPayload,
          ),
        ).rejects.toThrow('Stripe API error');
      });
    });

    describe('buildPaymentData', () => {
      it('should build payment data for user payment', () => {
        const metadata = {
          amount: '100',
          paymentType: PaymentType.USER,
          serviceType: ServiceType.SERVICE,
          serviceId: 'service-123',
          userId: 'user-123',
          stripe_session_id: 'cs_test_123',
          stripe_payment_intent_id: 'pi_test_123',
        };

        const result = (service as any).buildPaymentData(metadata, 'paid');

        expect(result).toEqual({
          amount: 100,
          status: 'paid',
          payment_type: PaymentType.USER,
          service_type: ServiceType.SERVICE,
          progress: 'Pending',
          stripe_session_id: 'cs_test_123',
          stripe_payment_intent_id: 'pi_test_123',
          payment_method: null,
          transaction_id: null,
          userId: 'user-123',
          serviceId: 'service-123',
        });
      });

      it('should build payment data for guest payment', () => {
        const metadata = {
          amount: '200',
          paymentType: PaymentType.GUEST,
          serviceType: ServiceType.PACKAGE,
          serviceId: 'package-123',
          guestName: 'John Guest',
          guestEmail: '<EMAIL>',
          guestMobile: '+**********',
          stripe_session_id: 'cs_test_456',
          stripe_payment_intent_id: 'pi_test_456',
        };

        const result = (service as any).buildPaymentData(metadata, 'failed');

        expect(result).toEqual({
          amount: 200,
          status: 'failed',
          payment_type: PaymentType.GUEST,
          service_type: ServiceType.PACKAGE,
          progress: 'Pending',
          stripe_session_id: 'cs_test_456',
          stripe_payment_intent_id: 'pi_test_456',
          payment_method: null,
          transaction_id: null,
          guest_name: 'John Guest',
          guest_email: '<EMAIL>',
          guest_mobile: '+**********',
          packageId: 'package-123',
        });
      });
    });

    describe('getServiceIdField', () => {
      it('should return correct field name for each service type', () => {
        expect((service as any).getServiceIdField('service')).toBe('serviceId');
        expect((service as any).getServiceIdField('package')).toBe('packageId');
        expect((service as any).getServiceIdField('immigration')).toBe(
          'immigration_serviceId',
        );
        expect((service as any).getServiceIdField('training')).toBe(
          'trainingId',
        );
      });

      it('should return serviceId for unknown service type', () => {
        expect((service as any).getServiceIdField('unknown')).toBe('serviceId');
      });
    });

    describe('getEmailConfig', () => {
      it('should return correct email config for service type', () => {
        const config = (service as any).getEmailConfig('service');

        expect(config).toEqual({
          customerSubject: 'Unlock Your Potential with Professional Mentorship',
          adminSubject: 'Appointment booked with mentor',
          serviceName: 'Mentor Service',
        });
      });

      it('should return correct email config for package type', () => {
        const config = (service as any).getEmailConfig('package');

        expect(config).toEqual({
          customerSubject: 'Package Purchase Confirmation',
          adminSubject: 'Package purchased',
          serviceName: 'Service Package',
        });
      });

      it('should return correct email config for immigration type', () => {
        const config = (service as any).getEmailConfig('immigration');

        expect(config).toEqual({
          customerSubject: 'Immigration Service Confirmation',
          adminSubject: 'Immigration service purchased',
          serviceName: 'Immigration Service',
        });
      });

      it('should return correct email config for training type', () => {
        const config = (service as any).getEmailConfig('training');

        expect(config).toEqual({
          customerSubject: 'Training Program Enrollment Confirmation',
          adminSubject: 'Training program purchased',
          serviceName: 'Training Program',
        });
      });

      it('should return default config for unknown service type', () => {
        const config = (service as any).getEmailConfig('unknown');

        expect(config).toEqual({
          customerSubject: 'Unlock Your Potential with Professional Mentorship',
          adminSubject: 'Appointment booked with mentor',
          serviceName: 'Mentor Service',
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle unexpected errors gracefully', async () => {
      // Arrange
      mockPrismaService.service.findUnique.mockImplementation(() => {
        throw new Error('Unexpected database error');
      });

      // Act & Assert
      await expect(
        service.createPayment(mockJWTPayload, validUserPaymentDto),
      ).rejects.toThrow('Unexpected database error');
    });

    it('should handle Stripe API rate limiting', async () => {
      // Arrange
      mockPrismaService.service.findUnique.mockResolvedValue(mockServiceData);
      // Mock user validation
      mockPrismaService.user.findUnique.mockResolvedValue({
        id: mockJWTPayload.id,
      });
      mockStripeClient.checkout.sessions.create.mockRejectedValue(
        new Error('Rate limit exceeded'),
      );

      // Act & Assert
      await expect(
        service.createPayment(mockJWTPayload, validUserPaymentDto),
      ).rejects.toThrow('Rate limit exceeded');
    });

    it('should handle network connectivity issues', async () => {
      // Arrange
      mockPrismaService.service.findUnique.mockResolvedValue(mockServiceData);
      // Mock user validation
      mockPrismaService.user.findUnique.mockResolvedValue({
        id: mockJWTPayload.id,
      });
      mockStripeClient.checkout.sessions.create.mockRejectedValue(
        new Error('Network error'),
      );

      // Act & Assert
      await expect(
        service.createPayment(mockJWTPayload, validUserPaymentDto),
      ).rejects.toThrow('Network error');
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complete payment flow for all service types', async () => {
      // Test all service types in sequence
      const testCases = [
        {
          dto: validUserPaymentDto,
          mockData: mockServiceData,
          prismaMethod: 'service',
        },
        {
          dto: validPackagePaymentDto,
          mockData: mockPackageData,
          prismaMethod: 'packages',
        },
        {
          dto: validImmigrationPaymentDto,
          mockData: mockImmigrationServiceData,
          prismaMethod: 'immigration_service',
        },
        {
          dto: validTrainingPaymentDto,
          mockData: mockTrainingData,
          prismaMethod: 'training',
        },
      ];

      for (const testCase of testCases) {
        // Reset mocks for each iteration
        resetAllMocks();

        // Arrange
        mockPrismaService[testCase.prismaMethod].findUnique.mockResolvedValue(
          testCase.mockData,
        );
        // Mock user validation
        mockPrismaService.user.findUnique.mockResolvedValue({
          id: mockJWTPayload.id,
        });
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createPayment(
          mockJWTPayload,
          testCase.dto,
        );

        // Assert
        expect(result.status).toBe('OK');
        expect(result.url).toBe(mockStripeSession.url);
      }
    });

    it('should handle concurrent payment creation requests', async () => {
      // Arrange
      mockPrismaService.service.findUnique.mockResolvedValue(mockServiceData);
      // Mock user validation
      mockPrismaService.user.findUnique.mockResolvedValue({
        id: mockJWTPayload.id,
      });
      mockStripeClient.checkout.sessions.create.mockResolvedValue(
        mockStripeSession,
      );

      // Act - Create multiple concurrent payment requests
      const promises = Array(5)
        .fill(null)
        .map(() => service.createPayment(mockJWTPayload, validUserPaymentDto));
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(5);
      results.forEach((result) => {
        expect(result.status).toBe('OK');
      });
      expect(prisma.service.findUnique).toHaveBeenCalledTimes(5);
      expect(stripe.checkout.sessions.create).toHaveBeenCalledTimes(5);
    });
  });

  // ========================================
  // ADMIN PAYMENT METHODS TESTS
  // ========================================

  describe('Admin Payment Methods', () => {
    describe('updatePaymentProgress', () => {
      it('should update payment progress successfully', async () => {
        // Arrange
        mockPrismaService.payment.update.mockResolvedValue(mockUpdatedPayment);

        // Act
        const result = await service.updatePaymentProgress(
          validAdminProgressDto,
        );

        // Assert
        expect(result).toEqual(mockUpdatedPayment);
        expect(prisma.payment.update).toHaveBeenCalledWith({
          where: { id: validAdminProgressDto.paymentId },
          data: {
            progress: validAdminProgressDto.progress,
            updatedAt: expect.any(Date),
          },
        });
      });

      it('should handle payment not found error', async () => {
        // Arrange
        const error = { ...mockPrismaNotFoundError };
        mockPrismaService.payment.update.mockRejectedValue(error);

        // Act & Assert
        await expect(
          service.updatePaymentProgress(validAdminProgressDto),
        ).rejects.toThrow(NotFoundException);
      });

      it('should handle database errors', async () => {
        // Arrange
        const error = new Error('Database connection failed');
        mockPrismaService.payment.update.mockRejectedValue(error);

        // Act & Assert
        await expect(
          service.updatePaymentProgress(validAdminProgressDto),
        ).rejects.toThrow(BadRequestException);
      });
    });
  });

  // ========================================
  // ENHANCED PAYMENT METADATA TESTS
  // ========================================

  describe('Payment Metadata Extraction', () => {
    describe('extractPaymentMetadata', () => {
      beforeEach(() => {
        resetAllMocks();
      });

      it('should extract payment method and transaction ID from Stripe session', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          payment_intent: 'pi_test_123',
          payment_method_types: ['card'],
        };

        const mockPaymentIntent = {
          id: 'pi_test_123',
          payment_method: {
            id: 'pm_test_123',
            type: 'card',
          },
          latest_charge: {
            id: 'ch_test_123',
          },
        };

        mockStripeClient.paymentIntents.retrieve.mockResolvedValue(
          mockPaymentIntent,
        );

        // Act
        const result = await (service as any).extractPaymentMetadata(
          mockSession,
        );

        // Assert
        expect(result).toEqual({
          paymentIntentId: 'pi_test_123',
          paymentMethod: 'card',
          transactionId: 'ch_test_123',
        });

        expect(stripe.paymentIntents.retrieve).toHaveBeenCalledWith(
          'pi_test_123',
          {
            expand: ['latest_charge', 'payment_method'],
          },
        );
      });

      it('should handle payment method as string ID', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          payment_intent: 'pi_test_123',
        };

        const mockPaymentIntent = {
          id: 'pi_test_123',
          payment_method: 'pm_test_123', // String ID instead of object
          latest_charge: 'ch_test_123', // String ID instead of object
        };

        const mockPaymentMethod = {
          id: 'pm_test_123',
          type: 'bank_transfer',
        };

        mockStripeClient.paymentIntents.retrieve.mockResolvedValue(
          mockPaymentIntent,
        );
        mockStripeClient.paymentMethods.retrieve.mockResolvedValue(
          mockPaymentMethod,
        );

        // Act
        const result = await (service as any).extractPaymentMetadata(
          mockSession,
        );

        // Assert
        expect(result).toEqual({
          paymentIntentId: 'pi_test_123',
          paymentMethod: 'bank_transfer',
          transactionId: 'ch_test_123',
        });

        expect(stripe.paymentMethods.retrieve).toHaveBeenCalledWith(
          'pm_test_123',
        );
      });

      it('should fallback to session payment_method_types when PaymentIntent fails', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          payment_intent: 'pi_test_123',
          payment_method_types: ['sepa_debit'],
        };

        mockStripeClient.paymentIntents.retrieve.mockRejectedValue(
          new Error('PaymentIntent not found'),
        );

        // Act
        const result = await (service as any).extractPaymentMetadata(
          mockSession,
        );

        // Assert
        expect(result).toEqual({
          paymentIntentId: 'pi_test_123',
          paymentMethod: 'sepa_debit',
          transactionId: null,
        });
      });

      it('should return null values when no payment intent exists', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          // No payment_intent
        };

        // Act
        const result = await (service as any).extractPaymentMetadata(
          mockSession,
        );

        // Assert
        expect(result).toEqual({
          paymentIntentId: null,
          paymentMethod: null,
          transactionId: null,
        });

        expect(stripe.paymentIntents.retrieve).not.toHaveBeenCalled();
      });

      it('should handle errors gracefully and return null values', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          payment_intent: 'pi_test_123',
        };

        mockStripeClient.paymentIntents.retrieve.mockRejectedValue(
          new Error('Stripe API error'),
        );

        // Act
        const result = await (service as any).extractPaymentMetadata(
          mockSession,
        );

        // Assert
        expect(result).toEqual({
          paymentIntentId: 'pi_test_123',
          paymentMethod: null,
          transactionId: null,
        });
      });
    });

    describe('Enhanced buildPaymentData', () => {
      it('should include payment metadata in payment data', () => {
        // Arrange
        const metadata = {
          amount: '100',
          paymentType: PaymentType.USER,
          serviceType: ServiceType.SERVICE,
          serviceId: 'service-123',
          userId: 'user-123',
          stripe_session_id: 'cs_test_123',
          stripe_payment_intent_id: 'pi_test_123',
        };

        const paymentMetadata = {
          paymentIntentId: 'pi_test_enhanced_123',
          paymentMethod: 'card',
          transactionId: 'ch_test_enhanced_123',
        };

        // Act
        const result = (service as any).buildPaymentData(
          metadata,
          'paid',
          paymentMetadata,
        );

        // Assert
        expect(result).toEqual({
          amount: 100,
          status: 'paid',
          payment_type: PaymentType.USER,
          service_type: ServiceType.SERVICE,
          progress: 'Pending',
          stripe_session_id: 'cs_test_123',
          stripe_payment_intent_id: 'pi_test_enhanced_123',
          payment_method: 'card',
          transaction_id: 'ch_test_enhanced_123',
          userId: 'user-123',
          serviceId: 'service-123',
        });
      });

      it('should fallback to metadata values when paymentMetadata is not provided', () => {
        // Arrange
        const metadata = {
          amount: '200',
          paymentType: PaymentType.GUEST,
          serviceType: ServiceType.PACKAGE,
          serviceId: 'package-123',
          guestName: 'John Guest',
          guestEmail: '<EMAIL>',
          guestMobile: '+**********',
          stripe_session_id: 'cs_test_456',
          stripe_payment_intent_id: 'pi_test_456',
        };

        // Act - No paymentMetadata provided
        const result = (service as any).buildPaymentData(metadata, 'failed');

        // Assert
        expect(result).toEqual({
          amount: 200,
          status: 'failed',
          payment_type: PaymentType.GUEST,
          service_type: ServiceType.PACKAGE,
          progress: 'Pending',
          stripe_session_id: 'cs_test_456',
          stripe_payment_intent_id: 'pi_test_456',
          payment_method: null,
          transaction_id: null,
          guest_name: 'John Guest',
          guest_email: '<EMAIL>',
          guest_mobile: '+**********',
          packageId: 'package-123',
        });
      });
    });
  });

  describe('Enhanced Webhook Processing', () => {
    describe('handlePaymentSuccess with Enhanced Metadata', () => {
      beforeEach(() => {
        resetAllMocks();
      });

      it('should update existing payment with enhanced metadata', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          payment_intent: 'pi_test_123',
          payment_method_types: ['card'],
          metadata: {
            paymentId: 'payment_test_123',
            serviceType: 'service',
            serviceId: 'service_test_123',
            paymentType: 'user',
            userId: 'user_test_123',
            amount: '10000',
          },
        };

        const mockPaymentIntent = {
          id: 'pi_test_123',
          payment_method: { type: 'card' },
          latest_charge: { id: 'ch_test_123' },
        };

        const existingPayment = {
          id: 'payment_test_123',
          status: 'pending',
        };

        const updatedPayment = {
          ...existingPayment,
          status: 'paid',
          stripe_payment_intent_id: 'pi_test_123',
          payment_method: 'card',
          transaction_id: 'ch_test_123',
        };

        mockPrismaService.payment.findFirst.mockResolvedValue(existingPayment);
        mockPrismaService.payment.update.mockResolvedValue(updatedPayment);
        mockStripeClient.paymentIntents.retrieve.mockResolvedValue(
          mockPaymentIntent,
        );
        mockMailerService.sendEmail.mockResolvedValue(true);

        // Act
        await (service as any).handlePaymentSuccess(mockSession);

        // Assert
        expect(prisma.payment.update).toHaveBeenCalledWith({
          where: { id: 'payment_test_123' },
          data: {
            status: 'paid',
            stripe_payment_intent_id: 'pi_test_123',
            payment_method: 'card',
            transaction_id: 'ch_test_123',
            updatedAt: expect.any(Date),
          },
        });
      });

      it('should create new payment with enhanced metadata when existing payment not found', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          payment_intent: 'pi_test_123',
          payment_method_types: ['bank_transfer'],
          metadata: {
            paymentId: 'payment_test_123',
            serviceType: 'service',
            serviceId: 'service_test_123',
            paymentType: 'guest',
            guestName: 'John Guest',
            guestEmail: '<EMAIL>',
            guestMobile: '+**********',
            amount: '15000',
          },
        };

        const mockPaymentIntent = {
          id: 'pi_test_123',
          payment_method: { type: 'bank_transfer' },
          latest_charge: { id: 'ch_test_456' },
        };

        const newPayment = {
          id: 'payment_new_123',
          status: 'paid',
          payment_method: 'bank_transfer',
          transaction_id: 'ch_test_456',
        };

        mockPrismaService.payment.findFirst.mockResolvedValue(null);
        mockPrismaService.payment.create.mockResolvedValue(newPayment);
        mockStripeClient.paymentIntents.retrieve.mockResolvedValue(
          mockPaymentIntent,
        );
        mockMailerService.sendEmail.mockResolvedValue(true);

        // Act
        await (service as any).handlePaymentSuccess(mockSession);

        // Assert
        expect(prisma.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            status: 'paid',
            payment_method: 'bank_transfer',
            transaction_id: 'ch_test_456',
            stripe_payment_intent_id: 'pi_test_123',
          }),
        });
      });
    });

    describe('handlePaymentFailure with Enhanced Metadata', () => {
      beforeEach(() => {
        resetAllMocks();
      });

      it('should update failed payment with enhanced metadata', async () => {
        // Arrange
        const mockSession = {
          id: 'cs_test_123',
          payment_intent: 'pi_test_123',
          payment_method_types: ['card'],
          metadata: {
            paymentId: 'payment_test_123',
            serviceType: 'service',
            serviceId: 'service_test_123',
            paymentType: 'user',
            userId: 'user_test_123',
            amount: '10000',
          },
        };

        const mockPaymentIntent = {
          id: 'pi_test_123',
          payment_method: { type: 'card' },
          latest_charge: { id: 'ch_test_123' },
        };

        const existingPayment = {
          id: 'payment_test_123',
          status: 'pending',
        };

        const updatedPayment = {
          ...existingPayment,
          status: 'failed',
          stripe_payment_intent_id: 'pi_test_123',
          payment_method: 'card',
          transaction_id: 'ch_test_123',
        };

        mockPrismaService.payment.findFirst.mockResolvedValue(existingPayment);
        mockPrismaService.payment.update.mockResolvedValue(updatedPayment);
        mockStripeClient.paymentIntents.retrieve.mockResolvedValue(
          mockPaymentIntent,
        );

        // Act
        await (service as any).handlePaymentFailure(mockSession);

        // Assert
        expect(prisma.payment.update).toHaveBeenCalledWith({
          where: { id: 'payment_test_123' },
          data: {
            status: 'failed',
            stripe_payment_intent_id: 'pi_test_123',
            payment_method: 'card',
            transaction_id: 'ch_test_123',
            updatedAt: expect.any(Date),
          },
        });
      });
    });
  });

  describe('createMultiMethodPayment', () => {
    const validMultiMethodPaymentDto = {
      amount: 150,
      user_id: 'user123',
      serviceType: 'immigration',
      serviceId: 'imm_service_123',
      discount_amount: 50,
      actual_amount: 200,
      payment_method: 'stripe',
    };

    const mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      name: 'Test User',
    };

    const mockImmigrationService = {
      id: 'imm_service_123',
      name: 'Visa Consultation',
      amount: 200,
    };

    const mockPayment = {
      id: 'pay_123456789',
      amount: 150,
      actual_amount: 200,
      discount_amount: 50,
      status: 'pending',
      payment_type: 'user',
      service_type: 'immigration',
      payment_method: 'stripe',
      progress: 'Pending',
      userId: 'user123',
      immigration_serviceId: 'imm_service_123',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    beforeEach(() => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.immigration_service.findUnique.mockResolvedValue(
        mockImmigrationService,
      );
      mockPrismaService.payment.create.mockResolvedValue(mockPayment);
      mockPrismaService.payment.update.mockResolvedValue(mockPayment);
    });

    describe('Stripe Payment Method', () => {
      it('should create stripe payment and return payment ID with stripe link', async () => {
        // Arrange
        const stripeDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'stripe',
        };
        const mockStripeSession = {
          id: 'cs_test_123',
          url: 'https://checkout.stripe.com/pay/cs_test_123',
        };
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createMultiMethodPayment(stripeDto);

        // Assert
        expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
          where: { id: 'user123' },
          select: { id: true, email: true, name: true },
        });
        expect(
          mockPrismaService.immigration_service.findUnique,
        ).toHaveBeenCalledWith({
          where: { id: 'imm_service_123' },
          select: { id: true, name: true, amount: true },
        });
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            amount: 150,
            actual_amount: 200,
            discount_amount: 50,
            status: 'pending',
            payment_type: 'user',
            service_type: 'immigration',
            payment_method: 'card',
            userId: 'user123',
            immigration_serviceId: 'imm_service_123',
          }),
        });
        expect(mockStripeClient.checkout.sessions.create).toHaveBeenCalledWith({
          line_items: [
            {
              price_data: {
                currency: 'eur',
                product_data: {
                  name: 'Visa Consultation',
                  description: 'immigration Service',
                },
                unit_amount: 15000, // 150 * 100
              },
              quantity: 1,
            },
          ],
          metadata: expect.objectContaining({
            paymentId: 'pay_123456789',
            serviceType: 'immigration',
            serviceId: 'imm_service_123',
            paymentType: 'user',
            userId: 'user123',
            amount: '150',
            paymentMethod: 'stripe',
          }),
          mode: 'payment',
          success_url: process.env.SUCCESS_URL,
          cancel_url: process.env.CANCELED_URL,
        });
        expect(mockPrismaService.payment.update).toHaveBeenCalledWith({
          where: { id: 'pay_123456789' },
          data: { stripe_session_id: 'cs_test_123' },
        });
        expect(result).toEqual({
          payment_id: 'pay_123456789',
          stripe_link: 'https://checkout.stripe.com/pay/cs_test_123',
        });
      });
    });

    describe('Non-Stripe Payment Methods', () => {
      it('should create cash payment and return only payment ID', async () => {
        // Arrange
        const cashDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'cash',
          transactionId: 'CASH_TXN_123',
        };

        // Act
        const result = await service.createMultiMethodPayment(cashDto);

        // Assert
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            payment_method: 'cash',
            transaction_id: 'CASH_TXN_123',
          }),
        });
        expect(
          mockStripeClient.checkout.sessions.create,
        ).not.toHaveBeenCalled();
        expect(result).toEqual({
          payment_id: 'pay_123456789',
        });
        expect(result.stripe_link).toBeUndefined();
      });

      it('should create bank_deposit payment and return only payment ID', async () => {
        // Arrange
        const bankDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'bank_deposit',
          transactionId: 'BANK_TXN_456',
        };

        // Act
        const result = await service.createMultiMethodPayment(bankDto);

        // Assert
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            payment_method: 'bank_deposit',
            transaction_id: 'BANK_TXN_456',
          }),
        });
        expect(
          mockStripeClient.checkout.sessions.create,
        ).not.toHaveBeenCalled();
        expect(result).toEqual({
          payment_id: 'pay_123456789',
        });
      });

      it('should create online_transfer payment and return only payment ID', async () => {
        // Arrange
        const transferDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'online_transfer',
          transactionId: 'ONLINE_TXN_789',
        };

        // Act
        const result = await service.createMultiMethodPayment(transferDto);

        // Assert
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            payment_method: 'online_transfer',
            transaction_id: 'ONLINE_TXN_789',
          }),
        });
        expect(
          mockStripeClient.checkout.sessions.create,
        ).not.toHaveBeenCalled();
        expect(result).toEqual({
          payment_id: 'pay_123456789',
        });
      });
    });

    describe('Validation Tests', () => {
      it('should throw error when user not found', async () => {
        // Arrange
        mockPrismaService.user.findUnique.mockResolvedValue(null);

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(validMultiMethodPaymentDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(validMultiMethodPaymentDto),
        ).rejects.toThrow(
          'User not found. Please ensure you are properly registered.',
        );
      });

      it('should throw error when immigration service not found', async () => {
        // Arrange
        mockPrismaService.immigration_service.findUnique.mockResolvedValue(
          null,
        );

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(validMultiMethodPaymentDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(validMultiMethodPaymentDto),
        ).rejects.toThrow('immigration service not found.');
      });

      it('should throw error when discount amount is greater than actual amount', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          discount_amount: 250,
          actual_amount: 200,
        };

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(
          'Discount amount cannot be greater than actual amount.',
        );
      });

      it('should throw error when amount calculation is incorrect', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          amount: 100, // Should be 150 (200 - 50)
          discount_amount: 50,
          actual_amount: 200,
        };

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(
          'Amount must equal actual amount minus discount amount.',
        );
      });

      it('should throw error when transactionId is missing for cash payment', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'cash',
          // transactionId is missing
        };

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow('Transaction ID is required for cash payments.');
      });

      it('should throw error when transactionId is empty for bank_deposit payment', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'bank_deposit',
          transactionId: '',
        };

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(
          'Transaction ID is required for bank_deposit payments.',
        );
      });

      it('should throw error when transactionId is too short', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'cash',
          transactionId: 'AB',
        };

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(
          'Transaction ID must be between 3 and 100 characters long.',
        );
      });

      it('should throw error when transactionId is too long', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'online_transfer',
          transactionId: 'A'.repeat(101),
        };

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
        await expect(
          service.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(
          'Transaction ID must be between 3 and 100 characters long.',
        );
      });

      it('should accept valid transactionId for non-stripe payments', async () => {
        // Arrange
        const validDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'cash',
          transactionId: 'VALID_TRANSACTION_123',
        };

        // Act
        const result = await service.createMultiMethodPayment(validDto);

        // Assert
        expect(result).toEqual({ payment_id: 'pay_123456789' });
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            transaction_id: 'VALID_TRANSACTION_123',
          }),
        });
      });

      it('should accept stripe payment without transactionId', async () => {
        // Arrange
        const stripeDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'stripe',
          // transactionId is not provided
        };

        const mockStripeSession = {
          id: 'cs_test_123',
          url: 'https://checkout.stripe.com/pay/cs_test_123',
        };
        mockStripeClient.checkout.sessions.create.mockResolvedValue(
          mockStripeSession,
        );

        // Act
        const result = await service.createMultiMethodPayment(stripeDto);

        // Assert
        expect(result).toEqual({
          payment_id: 'pay_123456789',
          stripe_link: 'https://checkout.stripe.com/pay/cs_test_123',
        });
        expect(mockPrismaService.payment.create).toHaveBeenCalledWith({
          data: expect.objectContaining({
            transaction_id: undefined,
          }),
        });
      });
    });

    describe('Error Handling', () => {
      it('should handle database errors gracefully', async () => {
        // Arrange
        mockPrismaService.payment.create.mockRejectedValue(
          new Error('Database connection failed'),
        );

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(validMultiMethodPaymentDto),
        ).rejects.toThrow(BadRequestException);
      });

      it('should handle Stripe API errors for stripe payments', async () => {
        // Arrange
        const stripeDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'stripe',
        };
        mockStripeClient.checkout.sessions.create.mockRejectedValue(
          new Error('Stripe API error: Invalid currency'),
        );

        // Act & Assert
        await expect(
          service.createMultiMethodPayment(stripeDto),
        ).rejects.toThrow(BadRequestException);
      });

      it('should log errors appropriately', async () => {
        // Arrange
        mockPrismaService.payment.create.mockRejectedValue(
          new Error('Database error'),
        );

        // Act
        try {
          await service.createMultiMethodPayment(validMultiMethodPaymentDto);
        } catch (error) {
          // Expected to throw
        }

        // Assert
        expect(mockLoggerService.error).toHaveBeenCalledWith(
          'Multi-method payment creation failed',
          expect.any(Error),
          expect.objectContaining({
            paymentMethod: 'stripe',
            amount: 150,
            userId: 'user123',
            serviceType: 'immigration',
          }),
        );
      });
    });
  });
});
