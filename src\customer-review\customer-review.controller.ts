import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CustomerReviewService } from './customer-review.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { CustomerReviewDto } from './dto/customer-review.dto';

@ApiTags('customer-review')
@Controller('customer-review')
export class CustomerReviewController {
  constructor(private customer: CustomerReviewService) {}

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async create(@Body() dto: CustomerReviewDto) {
    return await this.customer.create(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/:id')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async update(@Param('id') id: string, @Body() dto: CustomerReviewDto) {
    return await this.customer.update(id, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/:id')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async remove(@Param('id') id: string) {
    return await this.customer.remove(id);
  }

  @Get('')
  async getBlogs(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.customer.getAll(page, limit);
  }

  @Get('/:id')
  async getBlog(@Param('id') id: string) {
    return await this.customer.get(id);
  }
}
