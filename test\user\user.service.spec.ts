/**
 * User Service Unit Tests
 *
 * Comprehensive test suite for user registration and authentication functionality.
 * Tests all scenarios including optional password and mobile number support.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConflictException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UserService } from '../../src/user/user.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { OtpService } from '../../src/otp/otp.service';
import {
  CreateUserDto,
  LoginDto,
  UpdateUserDto,
} from '../../src/user/dto/user.dto';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('UserService', () => {
  let service: UserService;
  let prismaService: jest.Mocked<PrismaService>;
  let otpService: jest.Mocked<OtpService>;
  let jwtService: jest.Mocked<JwtService>;

  const mockPrismaService = {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  const mockOtpService = {
    generateOTPToken: jest.fn(),
    verifyOTPToken: jest.fn(),
  };

  const mockJwtService = {
    signAsync: jest.fn(),
    verifyAsync: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: OtpService,
          useValue: mockOtpService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    prismaService = module.get(PrismaService);
    otpService = module.get(OtpService);
    jwtService = module.get(JwtService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('create (User Registration)', () => {
    const baseUserDto: CreateUserDto = {
      name: 'John Doe',
      email: '<EMAIL>',
    };

    const mockOtpResponse = {
      token: 'otp-token-123',
      status: 'Ok',
      message: 'OTP sent successfully',
    };

    beforeEach(() => {
      mockOtpService.generateOTPToken.mockResolvedValue(mockOtpResponse);
    });

    it('should register user successfully with password and mobile number (emailVerified=false)', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
        mobileNo: '+353-1-234-5678',
        emailVerified: false, // Explicitly set to false
      };

      const mockCreatedUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashed_password',
        mobileNo: '+353-1-234-5678',
        emailVerified: false,
        provider: 'credentials',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);

      const result = await service.create(createUserDto);

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('password123', 10);
      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: {
          ...createUserDto,
          provider: 'credentials',
          password: 'hashed_password',
        },
      });
      expect(mockOtpService.generateOTPToken).toHaveBeenCalledWith({
        email: createUserDto.email,
        url: process.env.VERIFY_CLIENT_EMAIL_URL,
      });
      expect(result).toEqual({
        token: 'otp-token-123',
        status: 'Ok',
        message: 'Verify your email address',
      });
    });

    it('should register user successfully with emailVerified=true (no OTP sent)', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
        mobileNo: '+353-1-234-5678',
        emailVerified: true, // Email already verified
      };

      const mockCreatedUser = {
        id: 'user_123',
        name: createUserDto.name,
        email: createUserDto.email,
        emailVerified: true,
        password: 'hashed_password',
        mobileNo: createUserDto.mobileNo,
        provider: 'credentials',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);

      const result = await service.create(createUserDto);

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('password123', 10);
      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: {
          ...createUserDto,
          provider: 'credentials',
          password: 'hashed_password',
        },
      });
      expect(mockOtpService.generateOTPToken).not.toHaveBeenCalled(); // Should not send OTP
      expect(result).toEqual({
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required.',
        user: {
          id: mockCreatedUser.id,
          name: mockCreatedUser.name,
          email: mockCreatedUser.email,
          emailVerified: mockCreatedUser.emailVerified,
          mobileNo: mockCreatedUser.mobileNo,
          provider: mockCreatedUser.provider,
          createdAt: mockCreatedUser.createdAt,
          updatedAt: mockCreatedUser.updatedAt,
        },
      });
    });

    it('should register user successfully without password but with mobile number', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        mobileNo: '+353-1-234-5678',
      };

      const mockCreatedUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: null,
        mobileNo: '+353-1-234-5678',
        emailVerified: false,
        provider: 'credentials',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);

      const result = await service.create(createUserDto);

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(mockedBcrypt.hash).not.toHaveBeenCalled();
      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: {
          ...createUserDto,
          provider: 'credentials',
          password: null,
        },
      });
      expect(mockOtpService.generateOTPToken).not.toHaveBeenCalled();
      expect(result).toEqual({
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required for password-less registration.',
        user: {
          id: 'user_123',
          name: 'John Doe',
          email: '<EMAIL>',
          mobileNo: '+353-1-234-5678',
          emailVerified: false,
          provider: 'credentials',
          createdAt: mockCreatedUser.createdAt,
          updatedAt: mockCreatedUser.updatedAt,
        },
      });
    });

    it('should register user successfully with password but without mobile number', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
      };

      const mockCreatedUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashed_password',
        mobileNo: null,
        emailVerified: false,
        provider: 'credentials',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);

      const result = await service.create(createUserDto);

      expect(mockedBcrypt.hash).toHaveBeenCalledWith('password123', 10);
      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: {
          ...createUserDto,
          provider: 'credentials',
          password: 'hashed_password',
        },
      });
      expect(result).toEqual({
        token: 'otp-token-123',
        status: 'Ok',
        message: 'Verify your email address',
      });
    });

    it('should register user successfully without password and without mobile number', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
      };

      const mockCreatedUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: null,
        mobileNo: null,
        emailVerified: false,
        provider: 'credentials',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);

      const result = await service.create(createUserDto);

      expect(mockedBcrypt.hash).not.toHaveBeenCalled();
      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: {
          ...createUserDto,
          provider: 'credentials',
          password: null,
        },
      });
      expect(mockOtpService.generateOTPToken).not.toHaveBeenCalled();
      expect(result).toEqual({
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required for password-less registration.',
        user: {
          id: 'user_123',
          name: 'John Doe',
          email: '<EMAIL>',
          mobileNo: null,
          emailVerified: false,
          provider: 'credentials',
          createdAt: mockCreatedUser.createdAt,
          updatedAt: mockCreatedUser.updatedAt,
        },
      });
    });

    it('should throw ConflictException if user email already exists', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
      };

      const existingUser = {
        id: 'existing_user',
        email: '<EMAIL>',
        name: 'Existing User',
      };

      mockPrismaService.user.findUnique.mockResolvedValue(existingUser);

      await expect(service.create(createUserDto)).rejects.toThrow(
        ConflictException,
      );
      expect(mockPrismaService.user.create).not.toHaveBeenCalled();
    });

    it('should handle OTP generation failure gracefully for password registration', async () => {
      const createUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
      };

      const mockCreatedUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashed_password',
        emailVerified: false,
        provider: 'credentials',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);
      mockOtpService.generateOTPToken.mockRejectedValue(
        new Error('OTP service failed'),
      );

      await expect(service.create(createUserDto)).rejects.toThrow(
        'OTP service failed',
      );
    });

    it('should verify OTP service is called only when emailVerified is false', async () => {
      // Test user with emailVerified=false calls OTP
      const unverifiedUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
        emailVerified: false,
      };

      const mockCreatedUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashed_password',
        emailVerified: false,
        provider: 'credentials',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
      mockPrismaService.user.create.mockResolvedValue(mockCreatedUser);

      await service.create(unverifiedUserDto);

      expect(mockOtpService.generateOTPToken).toHaveBeenCalledWith({
        email: unverifiedUserDto.email,
        url: process.env.VERIFY_CLIENT_EMAIL_URL,
      });

      // Reset mocks
      jest.clearAllMocks();
      mockOtpService.generateOTPToken.mockResolvedValue({
        token: 'otp-token-123',
        status: 'Ok',
        message: 'OTP sent successfully',
      });

      // Test user with emailVerified=true does not call OTP
      const verifiedUserDto: CreateUserDto = {
        ...baseUserDto,
        password: 'password123',
        emailVerified: true,
      };

      const mockVerifiedUser = {
        ...mockCreatedUser,
        emailVerified: true,
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
      mockPrismaService.user.create.mockResolvedValue(mockVerifiedUser);

      await service.create(verifiedUserDto);

      expect(mockOtpService.generateOTPToken).not.toHaveBeenCalled();
    });
  });

  describe('validateUser (Login Validation)', () => {
    const loginDto: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should validate user with password successfully', async () => {
      const mockUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashed_password',
        mobileNo: '+353-1-234-5678',
        emailVerified: true,
        provider: 'credentials' as any,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'findByEmail').mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(true as never);

      const result = await service.validateUser(loginDto);

      expect(service.findByEmail).toHaveBeenCalledWith(loginDto.email);
      expect(mockedBcrypt.compare).toHaveBeenCalledWith(
        'password123',
        'hashed_password',
      );
      expect(result).toEqual({
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        mobileNo: '+353-1-234-5678',
        emailVerified: true,
        provider: 'credentials',
      });
    });

    it('should throw UnauthorizedException for user with null password', async () => {
      const mockUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: null,
        mobileNo: '+353-1-234-5678',
        emailVerified: true,
        provider: 'credentials' as any,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'findByEmail').mockResolvedValue(mockUser);

      await expect(service.validateUser(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
      expect(mockedBcrypt.compare).not.toHaveBeenCalled();
    });

    it('should throw UnauthorizedException for non-existent user', async () => {
      jest.spyOn(service, 'findByEmail').mockResolvedValue(null);

      await expect(service.validateUser(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw UnauthorizedException for incorrect password', async () => {
      const mockUser = {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'hashed_password',
        emailVerified: true,
        provider: 'credentials' as any,
        image: null,
        mobileNo: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'findByEmail').mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(false as never);

      await expect(service.validateUser(loginDto)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });
});
