import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsInt, IsDate, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class CustomerReviewDto {
  @ApiProperty({
    description: 'The name of the reviewer',
    example: '<PERSON>',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The image URL of the reviewer (optional)',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  img?: string;

  @ApiProperty({
    description: 'The review comment',
    example: 'Great service! Highly recommended.',
  })
  @IsString()
  comment: string;

  @ApiProperty({
    description: 'The source of the review',
    example: 'Google',
  })
  @IsString()
  source: string;

  @ApiProperty({
    description: 'The rating given by the reviewer',
    example: 5,
  })
  @IsInt()
  rating: number;

  @ApiProperty({ description: 'order' })
  @IsOptional()
  @IsInt()
  @Min(0)
  order: number;

  @ApiProperty({
    description: 'The date of the review',
    example: '2023-10-10T12:34:56Z',
  })
  @IsDate()
  @Type(() => Date)
  date: Date;
}
