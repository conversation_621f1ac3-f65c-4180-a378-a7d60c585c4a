/**
 * Workflow Master Integration Tests
 *
 * End-to-end integration tests for the Workflow Master module covering
 * database operations, API endpoints, and complete workflow scenarios.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import * as request from 'supertest';
import { WorkflowMasterModule } from '../../src/workflow/workflow-master.module';
import { PrismaService } from '../../src/utils/prisma.service';
import {
  CreateWorkflowMasterDto,
  UpdateWorkflowMasterDto,
} from '../../src/workflow/dto/workflow-master.dto';

describe('WorkflowMaster Integration Tests', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let authToken: string;

  // Test data
  const testWorkflowMaster = {
    name: 'Test Immigration Workflow',
    description: 'Test workflow for integration testing',
    is_active: true,
  };

  const updatedWorkflowMaster = {
    name: 'Updated Test Immigration Workflow',
    description: 'Updated test workflow description',
    is_active: false,
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '24h' },
        }),
        WorkflowMasterModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    prismaService = moduleFixture.get<PrismaService>(PrismaService);

    await app.init();

    // Generate test auth token (mock admin user)
    authToken = 'Bearer test-admin-token'; // In real tests, generate proper JWT
  });

  afterAll(async () => {
    // Clean up test data
    await prismaService.workflow_master.deleteMany({
      where: {
        name: {
          contains: 'Test',
        },
      },
    });

    await app.close();
  });

  beforeEach(async () => {
    // Clean up before each test
    await prismaService.workflow_master.deleteMany({
      where: {
        name: {
          contains: 'Test',
        },
      },
    });
  });

  describe('POST /workflow-master', () => {
    it('should create a new workflow master', async () => {
      const response = await request(app.getHttpServer())
        .post('/workflow-master')
        .set('Authorization', authToken)
        .send(testWorkflowMaster)
        .expect(201);

      expect(response.body).toMatchObject({
        name: testWorkflowMaster.name,
        description: testWorkflowMaster.description,
        is_active: testWorkflowMaster.is_active,
      });
      expect(response.body.id).toBeDefined();
      expect(response.body.created_at).toBeDefined();
      expect(response.body.updated_at).toBeDefined();
    });

    it('should return 409 for duplicate workflow master name', async () => {
      // Create first workflow master
      await request(app.getHttpServer())
        .post('/workflow-master')
        .set('Authorization', authToken)
        .send(testWorkflowMaster)
        .expect(201);

      // Try to create duplicate
      await request(app.getHttpServer())
        .post('/workflow-master')
        .set('Authorization', authToken)
        .send(testWorkflowMaster)
        .expect(409);
    });

    it('should return 400 for invalid input data', async () => {
      const invalidData = {
        name: '', // Empty name should fail validation
        description: testWorkflowMaster.description,
      };

      await request(app.getHttpServer())
        .post('/workflow-master')
        .set('Authorization', authToken)
        .send(invalidData)
        .expect(400);
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .post('/workflow-master')
        .send(testWorkflowMaster)
        .expect(401);
    });
  });

  describe('GET /workflow-master', () => {
    beforeEach(async () => {
      // Create test data
      await prismaService.workflow_master.createMany({
        data: [
          {
            name: 'Test Workflow 1',
            description: 'First test workflow',
            is_active: true,
          },
          {
            name: 'Test Workflow 2',
            description: 'Second test workflow',
            is_active: false,
          },
          {
            name: 'Another Test Workflow',
            description: 'Third test workflow',
            is_active: true,
          },
        ],
      });
    });

    it('should return paginated workflow masters', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-master')
        .set('Authorization', authToken)
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page', 1);
      expect(response.body).toHaveProperty('limit', 10);
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should filter workflow masters by search term', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-master')
        .set('Authorization', authToken)
        .query({ search: 'Another' })
        .expect(200);

      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].name).toContain('Another');
    });

    it('should filter workflow masters by active status', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-master')
        .set('Authorization', authToken)
        .query({ is_active: true })
        .expect(200);

      expect(response.body.data.every((wm: any) => wm.is_active === true)).toBe(
        true,
      );
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer()).get('/workflow-master').expect(401);
    });
  });

  describe('GET /workflow-master/active', () => {
    beforeEach(async () => {
      await prismaService.workflow_master.createMany({
        data: [
          {
            name: 'Active Test Workflow 1',
            description: 'Active workflow',
            is_active: true,
          },
          {
            name: 'Inactive Test Workflow',
            description: 'Inactive workflow',
            is_active: false,
          },
          {
            name: 'Active Test Workflow 2',
            description: 'Another active workflow',
            is_active: true,
          },
        ],
      });
    });

    it('should return only active workflow masters', async () => {
      const response = await request(app.getHttpServer())
        .get('/workflow-master/active')
        .set('Authorization', authToken)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.every((wm: any) => wm.is_active === true)).toBe(
        true,
      );
      expect(response.body.length).toBe(2);
    });
  });

  describe('GET /workflow-master/:id', () => {
    let workflowMasterId: string;

    beforeEach(async () => {
      const workflowMaster = await prismaService.workflow_master.create({
        data: testWorkflowMaster,
      });
      workflowMasterId = workflowMaster.id;
    });

    it('should return a workflow master by ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/workflow-master/${workflowMasterId}`)
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body).toMatchObject({
        id: workflowMasterId,
        name: testWorkflowMaster.name,
        description: testWorkflowMaster.description,
        is_active: testWorkflowMaster.is_active,
      });
    });

    it('should return 404 for non-existent workflow master', async () => {
      await request(app.getHttpServer())
        .get('/workflow-master/nonexistent-id')
        .set('Authorization', authToken)
        .expect(404);
    });
  });

  describe('PATCH /workflow-master/:id', () => {
    let workflowMasterId: string;

    beforeEach(async () => {
      const workflowMaster = await prismaService.workflow_master.create({
        data: testWorkflowMaster,
      });
      workflowMasterId = workflowMaster.id;
    });

    it('should update a workflow master', async () => {
      const response = await request(app.getHttpServer())
        .patch(`/workflow-master/${workflowMasterId}`)
        .set('Authorization', authToken)
        .send(updatedWorkflowMaster)
        .expect(200);

      expect(response.body).toMatchObject({
        id: workflowMasterId,
        name: updatedWorkflowMaster.name,
        description: updatedWorkflowMaster.description,
        is_active: updatedWorkflowMaster.is_active,
      });
    });

    it('should return 404 for non-existent workflow master', async () => {
      await request(app.getHttpServer())
        .patch('/workflow-master/nonexistent-id')
        .set('Authorization', authToken)
        .send(updatedWorkflowMaster)
        .expect(404);
    });
  });

  describe('PATCH /workflow-master/:id/toggle-active', () => {
    let workflowMasterId: string;

    beforeEach(async () => {
      const workflowMaster = await prismaService.workflow_master.create({
        data: testWorkflowMaster,
      });
      workflowMasterId = workflowMaster.id;
    });

    it('should toggle workflow master active status', async () => {
      const response = await request(app.getHttpServer())
        .patch(`/workflow-master/${workflowMasterId}/toggle-active`)
        .set('Authorization', authToken)
        .expect(200);

      expect(response.body.is_active).toBe(!testWorkflowMaster.is_active);
    });
  });

  describe('DELETE /workflow-master/:id', () => {
    let workflowMasterId: string;

    beforeEach(async () => {
      const workflowMaster = await prismaService.workflow_master.create({
        data: testWorkflowMaster,
      });
      workflowMasterId = workflowMaster.id;
    });

    it('should delete a workflow master', async () => {
      await request(app.getHttpServer())
        .delete(`/workflow-master/${workflowMasterId}`)
        .set('Authorization', authToken)
        .expect(200);

      // Verify deletion
      const deletedWorkflowMaster =
        await prismaService.workflow_master.findUnique({
          where: { id: workflowMasterId },
        });
      expect(deletedWorkflowMaster).toBeNull();
    });

    it('should return 404 for non-existent workflow master', async () => {
      await request(app.getHttpServer())
        .delete('/workflow-master/nonexistent-id')
        .set('Authorization', authToken)
        .expect(404);
    });
  });
});
