/**
 * Dashboard Refactored Service Unit Tests
 *
 * Comprehensive unit tests for the refactored DashboardRefactoredService using the
 * unified payment architecture. Tests cover all service methods with proper
 * mocking and error handling scenarios.
 *
 * Test Coverage:
 * - Main dashboard analytics method
 * - User and mentor counts
 * - Revenue analytics using unified payment table
 * - Top-rated mentors with performance metrics
 * - Latest users and contacts
 * - Error handling scenarios
 * - Performance and edge cases
 *
 * <AUTHOR> Ireland Development Team
 * @version 2.0.0
 * @since 2024-12-27
 */

import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { DashboardRefactoredService } from '../../src/dashboard/dashboard-refactored.service';
import { PrismaService } from '../../src/utils/prisma.service';
import {
  mockDashboardAnalytics,
  mockMentorsWithReviews,
  mockRevenueByServiceType,
  mockMentorRevenueData,
  mockUniqueUserPayments,
  mockLatestUsers,
  mockLatestContacts,
  mockTopRatedMentors,
  mockUserCounts,
  mockRevenueAnalytics,
  mockDatabaseError,
} from './dashboard-fixtures';

describe('DashboardRefactoredService', () => {
  let service: DashboardRefactoredService;
  let prismaService: jest.Mocked<PrismaService>;

  // Mock PrismaService
  const mockPrismaService = {
    user: {
      count: jest.fn(),
      findMany: jest.fn(),
    },
    mentor: {
      count: jest.fn(),
      findMany: jest.fn(),
    },
    payment: {
      groupBy: jest.fn(),
      aggregate: jest.fn(),
      findMany: jest.fn(),
    },
    contact_us: {
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DashboardRefactoredService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<DashboardRefactoredService>(
      DashboardRefactoredService,
    );
    prismaService = module.get(PrismaService);

    // Mock Logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('get', () => {
    it('should return complete dashboard analytics successfully', async () => {
      // Arrange
      mockPrismaService.user.count.mockResolvedValue(150);
      mockPrismaService.mentor.count.mockResolvedValue(25);
      mockPrismaService.payment.groupBy.mockResolvedValue(
        mockRevenueByServiceType,
      );
      mockPrismaService.mentor.findMany.mockResolvedValue(
        mockMentorsWithReviews,
      );
      mockPrismaService.payment.aggregate.mockResolvedValue(
        mockMentorRevenueData,
      );
      mockPrismaService.payment.findMany.mockResolvedValue(
        mockUniqueUserPayments,
      );
      mockPrismaService.user.findMany.mockResolvedValue(mockLatestUsers);
      mockPrismaService.contact_us.findMany.mockResolvedValue(
        mockLatestContacts,
      );

      // Act
      const result = await service.getDashboardAnalytics();

      // Assert
      expect(result).toBeDefined();
      expect(result.total_users).toBe('150');
      expect(result.total_mentors).toBe('25');
      expect(result.total_revenue).toBe('42000');
      expect(result.top_rated_mentors).toHaveLength(3); // All 3 mentors have reviews
      expect(result.latest_users).toHaveLength(3);
      expect(result.latest_contacts).toHaveLength(2);
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      mockPrismaService.user.count.mockRejectedValue(mockDatabaseError);

      // Act & Assert
      await expect(service.getDashboardAnalytics()).rejects.toThrow(
        'Database connection failed',
      );
    });

    it('should log analytics fetch start and completion', async () => {
      // Arrange
      const logSpy = jest.spyOn(Logger.prototype, 'log');
      mockPrismaService.user.count.mockResolvedValue(150);
      mockPrismaService.mentor.count.mockResolvedValue(25);
      mockPrismaService.payment.groupBy.mockResolvedValue([]);
      mockPrismaService.mentor.findMany.mockResolvedValue([]);
      mockPrismaService.user.findMany.mockResolvedValue([]);
      mockPrismaService.contact_us.findMany.mockResolvedValue([]);

      // Act
      await service.getDashboardAnalytics();

      // Assert
      expect(logSpy).toHaveBeenCalledWith(
        'Fetching dashboard analytics using unified payment architecture',
      );
      expect(logSpy).toHaveBeenCalledWith(
        'Dashboard analytics fetched successfully with unified payment data',
      );
    });
  });

  describe('getUserAndMentorCounts', () => {
    it('should return user and mentor counts as strings', async () => {
      // Arrange
      mockPrismaService.user.count.mockResolvedValue(150);
      mockPrismaService.mentor.count.mockResolvedValue(25);

      // Act
      const result = await (service as any).getUserAndMentorCounts();

      // Assert
      expect(result).toEqual({
        total_users: '150',
        total_mentors: '25',
      });
      expect(mockPrismaService.user.count).toHaveBeenCalledTimes(1);
      expect(mockPrismaService.mentor.count).toHaveBeenCalledTimes(1);
    });

    it('should handle zero counts correctly', async () => {
      // Arrange
      mockPrismaService.user.count.mockResolvedValue(0);
      mockPrismaService.mentor.count.mockResolvedValue(0);

      // Act
      const result = await (service as any).getUserAndMentorCounts();

      // Assert
      expect(result).toEqual({
        total_users: '0',
        total_mentors: '0',
      });
    });

    it('should handle database errors', async () => {
      // Arrange
      mockPrismaService.user.count.mockRejectedValue(mockDatabaseError);

      // Act & Assert
      await expect((service as any).getUserAndMentorCounts()).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('getUnifiedRevenueAnalytics', () => {
    it('should calculate revenue by service type correctly', async () => {
      // Arrange
      mockPrismaService.payment.groupBy.mockResolvedValue(
        mockRevenueByServiceType,
      );

      // Act
      const result = await (service as any).getUnifiedRevenueAnalytics();

      // Assert
      expect(result).toEqual({
        mentor_service_revenue: '15000',
        package_revenue: '8500',
        immigration_service_revenue: '12000',
        training_revenue: '6500',
        total_revenue: '42000',
      });
      expect(mockPrismaService.payment.groupBy).toHaveBeenCalledWith({
        by: ['service_type'],
        where: { status: 'paid' },
        _sum: { amount: true },
      });
    });

    it('should handle missing service types with zero revenue', async () => {
      // Arrange - Only mentor service has revenue
      mockPrismaService.payment.groupBy.mockResolvedValue([
        { service_type: 'mentor', _sum: { amount: 5000 } },
      ]);

      // Act
      const result = await (service as any).getUnifiedRevenueAnalytics();

      // Assert
      expect(result).toEqual({
        mentor_service_revenue: '5000',
        package_revenue: '0',
        immigration_service_revenue: '0',
        training_revenue: '0',
        total_revenue: '5000',
      });
    });

    it('should handle null amounts correctly', async () => {
      // Arrange
      mockPrismaService.payment.groupBy.mockResolvedValue([
        { service_type: 'mentor', _sum: { amount: null } },
      ]);

      // Act
      const result = await (service as any).getUnifiedRevenueAnalytics();

      // Assert
      expect(result.mentor_service_revenue).toBe('0');
      expect(result.total_revenue).toBe('0');
    });
  });

  describe('getTopRatedMentorsWithUnifiedPayments', () => {
    it('should return top-rated mentors with performance metrics', async () => {
      // Arrange
      mockPrismaService.mentor.findMany.mockResolvedValue(
        mockMentorsWithReviews,
      );
      mockPrismaService.payment.aggregate.mockResolvedValue(
        mockMentorRevenueData,
      );
      mockPrismaService.payment.findMany.mockResolvedValue(
        mockUniqueUserPayments,
      );

      // Act
      const result = await (
        service as any
      ).getTopRatedMentorsWithUnifiedPayments();

      // Assert
      expect(result).toHaveLength(3);
      expect(result[0].average_rating).toBe('4.67'); // John Doe with highest rating
      expect(result[0].review_count).toBe('3');
      expect(result[0].revenue_generated).toBe('5000');
      expect(result[0].total_clients).toBe('3');
    });

    it('should sort mentors by average rating in descending order', async () => {
      // Arrange
      mockPrismaService.mentor.findMany.mockResolvedValue(
        mockMentorsWithReviews,
      );
      mockPrismaService.payment.aggregate.mockResolvedValue(
        mockMentorRevenueData,
      );
      mockPrismaService.payment.findMany.mockResolvedValue(
        mockUniqueUserPayments,
      );

      // Act
      const result = await (
        service as any
      ).getTopRatedMentorsWithUnifiedPayments();

      // Assert
      const ratings = result.map((mentor) => parseFloat(mentor.average_rating));
      for (let i = 1; i < ratings.length; i++) {
        expect(ratings[i - 1]).toBeGreaterThanOrEqual(ratings[i]);
      }
    });

    it('should limit results to top 5 mentors', async () => {
      // Arrange - Create 7 mentors
      const manyMentors = Array.from({ length: 7 }, (_, i) => ({
        id: `mentor-${i}`,
        name: `Mentor ${i}`,
        image: null,
        reviews: [{ rating: 4 }],
      }));
      mockPrismaService.mentor.findMany.mockResolvedValue(manyMentors);
      mockPrismaService.payment.aggregate.mockResolvedValue({
        _sum: { amount: 1000 },
      });
      mockPrismaService.payment.findMany.mockResolvedValue([
        { userId: 'user-1' },
      ]);

      // Act
      const result = await (
        service as any
      ).getTopRatedMentorsWithUnifiedPayments();

      // Assert
      expect(result).toHaveLength(5);
    });

    it('should handle mentors with no revenue', async () => {
      // Arrange
      mockPrismaService.mentor.findMany.mockResolvedValue([
        mockMentorsWithReviews[0],
      ]);
      mockPrismaService.payment.aggregate.mockResolvedValue({
        _sum: { amount: null },
      });
      mockPrismaService.payment.findMany.mockResolvedValue([]);

      // Act
      const result = await (
        service as any
      ).getTopRatedMentorsWithUnifiedPayments();

      // Assert
      expect(result[0].revenue_generated).toBe('0');
      expect(result[0].total_clients).toBe('0');
    });

    it('should query payments with correct filters', async () => {
      // Arrange
      mockPrismaService.mentor.findMany.mockResolvedValue([
        mockMentorsWithReviews[0],
      ]);
      mockPrismaService.payment.aggregate.mockResolvedValue(
        mockMentorRevenueData,
      );
      mockPrismaService.payment.findMany.mockResolvedValue(
        mockUniqueUserPayments,
      );

      // Act
      await (service as any).getTopRatedMentorsWithUnifiedPayments();

      // Assert
      expect(mockPrismaService.payment.aggregate).toHaveBeenCalledWith({
        where: {
          service_type: 'mentor',
          status: 'paid',
          service: { mentorId: 'mentor-1' },
        },
        _sum: { amount: true },
      });
      expect(mockPrismaService.payment.findMany).toHaveBeenCalledWith({
        where: {
          service_type: 'mentor',
          status: 'paid',
          service: { mentorId: 'mentor-1' },
          userId: { not: null },
        },
        select: { userId: true },
        distinct: ['userId'],
      });
    });
  });

  describe('getLatestRegisteredUsers', () => {
    it('should return latest 5 users ordered by creation date', async () => {
      // Arrange
      mockPrismaService.user.findMany.mockResolvedValue(mockLatestUsers);

      // Act
      const result = await (service as any).getLatestRegisteredUsers();

      // Assert
      expect(result).toEqual(mockLatestUsers);
      expect(mockPrismaService.user.findMany).toHaveBeenCalledWith({
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      });
    });

    it('should handle empty user list', async () => {
      // Arrange
      mockPrismaService.user.findMany.mockResolvedValue([]);

      // Act
      const result = await (service as any).getLatestRegisteredUsers();

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('getLatestContactSubmissions', () => {
    it('should return latest 5 contacts ordered by creation date', async () => {
      // Arrange
      mockPrismaService.contact_us.findMany.mockResolvedValue(
        mockLatestContacts,
      );

      // Act
      const result = await (service as any).getLatestContactSubmissions();

      // Assert
      expect(result).toEqual(mockLatestContacts);
      expect(mockPrismaService.contact_us.findMany).toHaveBeenCalledWith({
        select: {
          id: true,
          name: true,
          email: true,
          message: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      });
    });

    it('should handle empty contact list', async () => {
      // Arrange
      mockPrismaService.contact_us.findMany.mockResolvedValue([]);

      // Act
      const result = await (service as any).getLatestContactSubmissions();

      // Assert
      expect(result).toEqual([]);
    });
  });
});
