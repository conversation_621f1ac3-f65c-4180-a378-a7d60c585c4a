/**
 * Auth Controller Tests
 *
 * Comprehensive test suite for AuthController.
 * Tests universal authentication endpoints that work across all user types.
 *
 * @desc Tests the universal auth API endpoints for token validation
 * @assumptions Uses mocked JwtGuard for authentication testing
 * @mocked_dependencies JwtGuard
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AuthController } from '../../src/auth/auth.controller';
import { JwtGuard } from '../../src/guards/jwt.guard';

describe('AuthController', () => {
  let controller: AuthController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
    })
      .overrideGuard(JwtGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AuthController>(AuthController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCurrentUserProfile', () => {
    describe('Success Cases', () => {
      it('should return user token type for user role', async () => {
        // Arrange
        const mockRequest = {
          user: {
            tokenType: 'user',
            id: 'user123',
          },
        };

        // Act
        const result = await controller.getCurrentUserProfile(mockRequest);

        // Assert
        expect(result).toEqual({
          tokenType: 'user',
        });
      });

      it('should return admin token type for admin role', async () => {
        // Arrange
        const mockRequest = {
          user: {
            tokenType: 'admin',
            id: 'admin123',
          },
        };

        // Act
        const result = await controller.getCurrentUserProfile(mockRequest);

        // Assert
        expect(result).toEqual({
          tokenType: 'admin',
        });
      });

      it('should return agent token type for agent role', async () => {
        // Arrange
        const mockRequest = {
          user: {
            tokenType: 'agent',
            id: 'agent123',
          },
        };

        // Act
        const result = await controller.getCurrentUserProfile(mockRequest);

        // Assert
        expect(result).toEqual({
          tokenType: 'agent',
        });
      });

      it('should return mentor token type for mentor role', async () => {
        // Arrange
        const mockRequest = {
          user: {
            tokenType: 'mentor',
            id: 'mentor123',
          },
        };

        // Act
        const result = await controller.getCurrentUserProfile(mockRequest);

        // Assert
        expect(result).toEqual({
          tokenType: 'mentor',
        });
      });
    });

    describe('Error Cases', () => {
      it('should throw HttpException when request user is undefined', async () => {
        // Arrange
        const mockRequest = {};

        // Act & Assert
        await expect(
          controller.getCurrentUserProfile(mockRequest),
        ).rejects.toThrow(HttpException);
      });

      it('should return undefined tokenType when tokenType is missing', async () => {
        // Arrange
        const mockRequest = {
          user: {
            id: 'user123',
            // tokenType is missing
          },
        };

        // Act
        const result = await controller.getCurrentUserProfile(mockRequest);

        // Assert
        expect(result).toEqual({
          tokenType: undefined,
        });
      });

      it('should throw HttpException with correct status and message', async () => {
        // Arrange
        const mockRequest = {};

        // Act & Assert
        try {
          await controller.getCurrentUserProfile(mockRequest);
        } catch (error) {
          expect(error).toBeInstanceOf(HttpException);
          expect(error.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
          expect(error.message).toBe('Failed to retrieve token type');
        }
      });
    });

    describe('Edge Cases', () => {
      it('should handle null user object', async () => {
        // Arrange
        const mockRequest = {
          user: null,
        };

        // Act & Assert
        await expect(
          controller.getCurrentUserProfile(mockRequest),
        ).rejects.toThrow(HttpException);
      });

      it('should handle empty tokenType string', async () => {
        // Arrange
        const mockRequest = {
          user: {
            tokenType: '',
            id: 'user123',
          },
        };

        // Act
        const result = await controller.getCurrentUserProfile(mockRequest);

        // Assert
        expect(result).toEqual({
          tokenType: '',
        });
      });

      it('should handle unexpected tokenType values', async () => {
        // Arrange
        const mockRequest = {
          user: {
            tokenType: 'unknown_role',
            id: 'user123',
          },
        };

        // Act
        const result = await controller.getCurrentUserProfile(mockRequest);

        // Assert
        expect(result).toEqual({
          tokenType: 'unknown_role',
        });
      });
    });
  });
});
