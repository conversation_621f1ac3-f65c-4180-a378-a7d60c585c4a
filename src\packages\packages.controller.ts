import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PackagesService } from './packages.service';
import { JwtAdmin } from 'src/guards/jwt.admin.guard';
import { PackageDto } from './dto/packages.dto';

@ApiTags('packages')
@Controller('packages')
export class PackagesController {
  constructor(private packages: PackagesService) {}
  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Post('')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async create(@Body() dto: PackageDto) {
    return await this.packages.create(dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Patch('/:packageId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async update(@Param('packageId') id: string, @Body() dto: PackageDto) {
    return await this.packages.update(id, dto);
  }

  @UseGuards(JwtAdmin)
  @ApiBearerAuth()
  @Delete('/:packageId')
  @ApiOperation({
    summary: '(Admin only)',
    description:
      'This API is restricted to admin users and requires a Bearer token for authentication.',
  })
  async remove(@Param('packageId') id: string) {
    return await this.packages.remove(id);
  }

  @Get('')
  async getAll() {
    return await this.packages.getAll();
  }
}
