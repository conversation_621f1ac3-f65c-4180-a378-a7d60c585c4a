/**
 * Unit Test for numberOfSteps Field Enhancement
 *
 * Tests the new numberOfSteps field calculation in ApplicationTransformerService
 * Verifies that the field is correctly calculated from workflow templates
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ApplicationTransformerService } from '../../src/application/services/application-transformer.service';

describe('NumberOfSteps Field Unit Tests', () => {
  let transformerService: ApplicationTransformerService;

  // Mock workflow template with 3 steps
  const mockWorkflowTemplate = [
    {
      stageName: 'Document Collection',
      stageOrder: 1,
      description: 'Collect all required documents',
      documentsRequired: true,
      customFormRequired: false,
    },
    {
      stageName: 'Review Process',
      stageOrder: 2,
      description: 'Review submitted documents',
      documentsRequired: false,
      customFormRequired: true,
    },
    {
      stageName: 'Final Approval',
      stageOrder: 3,
      description: 'Final approval and completion',
      documentsRequired: false,
      customFormRequired: false,
    },
  ];

  // Mock application with workflow template
  const mockApplicationWithWorkflow = {
    id: 'app_123',
    application_number: 'IMM-2024-001',
    service_type: 'immigration',
    status: 'Draft',
    priority_level: 'Medium',
    current_step: '1',
    user_id: 'user_123',
    workflow_template_id: 'template_123',
    created_at: new Date(),
    updated_at: new Date(),
    estimated_completion: null,
    user: {
      id: 'user_123',
      name: 'Test User',
      email: '<EMAIL>',
    },
    payment: {
      immigration_service: {
        name: 'Work Permit Application',
      },
    },
    assigned_agent: null,
    workflow_template: {
      id: 'template_123',
      name: 'Immigration Workflow',
      description: 'Standard immigration workflow',
      workflowTemplate: mockWorkflowTemplate,
    },
  };

  // Mock application without workflow template
  const mockApplicationWithoutWorkflow = {
    id: 'app_456',
    application_number: 'IMM-2024-002',
    service_type: 'immigration',
    status: 'Draft',
    priority_level: 'Medium',
    current_step: '1',
    user_id: 'user_123',
    workflow_template_id: null,
    created_at: new Date(),
    updated_at: new Date(),
    estimated_completion: null,
    user: {
      id: 'user_123',
      name: 'Test User',
      email: '<EMAIL>',
    },
    payment: {
      immigration_service: {
        name: 'Work Permit Application',
      },
    },
    assigned_agent: null,
    workflow_template: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ApplicationTransformerService],
    }).compile();

    transformerService = module.get<ApplicationTransformerService>(
      ApplicationTransformerService,
    );
  });

  describe('transformApplicationListItem with numberOfSteps', () => {
    it('should return correct numberOfSteps for application with workflow template', () => {
      const result = transformerService.transformApplicationListItem(
        mockApplicationWithWorkflow,
      );

      expect(result).toHaveProperty('numberOfSteps');
      expect(result.numberOfSteps).toBe(3); // Should match mockWorkflowTemplate length
      expect(typeof result.numberOfSteps).toBe('number');
    });

    it('should return 0 numberOfSteps for application without workflow template', () => {
      const result = transformerService.transformApplicationListItem(
        mockApplicationWithoutWorkflow,
      );

      expect(result).toHaveProperty('numberOfSteps');
      expect(result.numberOfSteps).toBe(0); // Should be 0 when no workflow template
      expect(typeof result.numberOfSteps).toBe('number');
    });

    it('should return 0 for malformed workflow template (not an array)', () => {
      const appWithMalformedWorkflow = {
        ...mockApplicationWithWorkflow,
        workflow_template: {
          id: 'template_123',
          name: 'Immigration Workflow',
          description: 'Standard immigration workflow',
          workflowTemplate: 'invalid-not-array', // Invalid format
        },
      };

      const result = transformerService.transformApplicationListItem(
        appWithMalformedWorkflow,
      );

      expect(result).toHaveProperty('numberOfSteps');
      expect(result.numberOfSteps).toBe(0);
    });

    it('should return 0 for empty workflow template array', () => {
      const appWithEmptyWorkflow = {
        ...mockApplicationWithWorkflow,
        workflow_template: {
          id: 'template_123',
          name: 'Immigration Workflow',
          description: 'Standard immigration workflow',
          workflowTemplate: [], // Empty array
        },
      };

      const result =
        transformerService.transformApplicationListItem(appWithEmptyWorkflow);

      expect(result).toHaveProperty('numberOfSteps');
      expect(result.numberOfSteps).toBe(0);
    });

    it('should handle workflow template with single step', () => {
      const singleStepWorkflow = [
        {
          stageName: 'Single Step',
          stageOrder: 1,
          description: 'Only one step',
          documentsRequired: true,
          customFormRequired: false,
        },
      ];

      const appWithSingleStep = {
        ...mockApplicationWithWorkflow,
        workflow_template: {
          id: 'template_123',
          name: 'Single Step Workflow',
          description: 'Workflow with one step',
          workflowTemplate: singleStepWorkflow,
        },
      };

      const result =
        transformerService.transformApplicationListItem(appWithSingleStep);

      expect(result).toHaveProperty('numberOfSteps');
      expect(result.numberOfSteps).toBe(1);
    });

    it('should handle workflow template with many steps', () => {
      const manyStepsWorkflow = Array.from({ length: 10 }, (_, index) => ({
        stageName: `Step ${index + 1}`,
        stageOrder: index + 1,
        description: `Description for step ${index + 1}`,
        documentsRequired: index % 2 === 0,
        customFormRequired: index % 3 === 0,
      }));

      const appWithManySteps = {
        ...mockApplicationWithWorkflow,
        workflow_template: {
          id: 'template_123',
          name: 'Complex Workflow',
          description: 'Workflow with many steps',
          workflowTemplate: manyStepsWorkflow,
        },
      };

      const result =
        transformerService.transformApplicationListItem(appWithManySteps);

      expect(result).toHaveProperty('numberOfSteps');
      expect(result.numberOfSteps).toBe(10);
    });

    it('should preserve all other fields in the transformed result', () => {
      const result = transformerService.transformApplicationListItem(
        mockApplicationWithWorkflow,
      );

      // Check that all expected fields are present
      expect(result).toHaveProperty('id', 'app_123');
      expect(result).toHaveProperty('application_number', 'IMM-2024-001');
      expect(result).toHaveProperty('service_type', 'immigration');
      expect(result).toHaveProperty('status', 'Draft');
      expect(result).toHaveProperty('priority_level', 'Medium');
      expect(result).toHaveProperty('current_step', '1');
      expect(result).toHaveProperty('numberOfSteps', 3);
      expect(result).toHaveProperty('service_name');
      expect(result).toHaveProperty('created_at');
      expect(result).toHaveProperty('updated_at');
      expect(result).toHaveProperty('estimated_completion');

      // Check user information
      expect(result).toHaveProperty('user');
      expect(result.user).toHaveProperty('name', 'Test User');
      expect(result.user).toHaveProperty('email', '<EMAIL>');
    });
  });

  describe('transformApplicationList with numberOfSteps', () => {
    it('should transform multiple applications with correct numberOfSteps', () => {
      const applications = [
        mockApplicationWithWorkflow,
        mockApplicationWithoutWorkflow,
      ];
      const result = transformerService.transformApplicationList(applications);

      expect(result).toHaveLength(2);

      // First application should have 3 steps
      expect(result[0]).toHaveProperty('numberOfSteps', 3);

      // Second application should have 0 steps
      expect(result[1]).toHaveProperty('numberOfSteps', 0);
    });
  });

  describe('createPaginatedResponse with numberOfSteps', () => {
    it('should create paginated response with numberOfSteps in data', () => {
      const applications = [mockApplicationWithWorkflow];
      const result = transformerService.createPaginatedResponse(
        applications,
        1,
        1,
        10,
      );

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toHaveProperty('numberOfSteps', 3);

      // Check pagination structure
      expect(result.pagination).toEqual({
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });
  });
});
