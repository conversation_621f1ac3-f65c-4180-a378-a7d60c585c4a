import {
  ConflictException,
  Injectable,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { compare, hash } from 'bcrypt';
import { randomBytes } from 'crypto';
import { PrismaService } from 'src/utils/prisma.service';
import { MailerService } from 'src/mailer/mailer.service';
import { LoggerService } from 'src/utils/logger.service';
import { render } from '@react-email/components';
import AgentWelcomeEmail from 'src/template/agent-welcome';
import AgentPasswordResetEmail from 'src/template/agent-password-reset';
import {
  CreateAgentDto,
  AgentLoginDto,
  UpdateAgentPasswordDto,
  ResetAgentPasswordDto,
  ConfirmResetAgentPasswordDto,
  UpdateAgentDto,
  AgentQueryDto,
} from './dto/agent.dto';
import {
  IAgent,
  ICreateAgentData,
  IUpdateAgentData,
  IAgentFilters,
  IAgentWithAdmin,
  IAgentAuthPayload,
} from './interfaces/agent.interfaces';
import { AgentStatus } from '@prisma/client';

const EXPIRE_TIME = 5 * 60 * 60 * 1000; // 5 hours

@Injectable()
export class AgentService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private mailerService: MailerService,
    private logger: LoggerService,
  ) {}

  /**
   * Register a new agent (admin-only)
   */
  async register(
    dto: CreateAgentDto,
    adminId: string,
  ): Promise<{
    agent: IAgent;
    temporaryPassword: string;
  }> {
    try {
      // Check if agent with email already exists
      const existingAgent = await this.findByEmail(dto.email);
      if (existingAgent) {
        throw new ConflictException('Agent with this email already exists');
      }

      // Verify admin exists
      const admin = await this.prisma.admin.findUnique({
        where: { id: adminId },
      });
      if (!admin) {
        throw new NotFoundException('Admin not found');
      }

      // Generate temporary password
      const temporaryPassword = this.generateTemporaryPassword();
      const hashedPassword = await this.hashPassword(temporaryPassword);

      // Create agent data
      const agentData: ICreateAgentData = {
        name: dto.name,
        email: dto.email,
        password_hash: hashedPassword,
        phone: dto.phone,
        status: dto.status || AgentStatus.Active,
        created_by_admin_id: adminId,
      };

      // Create agent in database
      const agent = await this.prisma.agent.create({
        data: agentData,
      });

      this.logger.info(
        `Agent created successfully: ${agent.email} by admin: ${admin.email}`,
      );

      // Send welcome email with temporary password
      await this.sendWelcomeEmail(agent, temporaryPassword);

      return {
        agent,
        temporaryPassword,
      };
    } catch (error) {
      this.logger.error(
        `Failed to register agent: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Agent login
   */
  async login(dto: AgentLoginDto): Promise<{
    user: Omit<IAgent, 'password_hash'>;
    backendTokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
  }> {
    try {
      const agent = await this.validateAgent(dto);

      // Check if agent is active
      if (agent.status !== AgentStatus.Active) {
        throw new UnauthorizedException('Agent account is not active');
      }

      const payload: IAgentAuthPayload = {
        id: agent.id,
        email: agent.email,
        tokenType: 'agent',
        sub: {
          name: agent.name,
        },
      };

      const { password_hash, ...agentWithoutPassword } = agent;

      return {
        user: agentWithoutPassword,
        backendTokens: {
          accessToken: await this.jwtService.signAsync(payload, {
            expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE || '5h',
            secret: process.env.jwtAgentSecretKey,
          }),
          refreshToken: await this.jwtService.signAsync(payload, {
            expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE || '7d',
            secret: process.env.jwtRefreshTokenKey,
          }),
          expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
        },
      };
    } catch (error) {
      this.logger.error(`Agent login failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update agent password
   */
  async updatePassword(
    agentId: string,
    dto: UpdateAgentPasswordDto,
  ): Promise<void> {
    try {
      const agent = await this.findById(agentId);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await this.validatePassword(
        dto.currentPassword,
        agent.password_hash,
      );
      if (!isCurrentPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await this.hashPassword(dto.newPassword);

      // Update password in database
      await this.prisma.agent.update({
        where: { id: agentId },
        data: { password_hash: hashedNewPassword },
      });

      this.logger.info(
        `Password updated successfully for agent: ${agent.email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update agent password: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Reset agent password (without login)
   */
  async resetPassword(dto: ResetAgentPasswordDto): Promise<void> {
    try {
      const agent = await this.findByEmail(dto.email);
      if (!agent) {
        // Don't reveal if email exists or not for security
        this.logger.warn(
          `Password reset attempted for non-existent agent: ${dto.email}`,
        );
        return;
      }

      // Generate reset token
      const resetToken = await this.jwtService.signAsync(
        { email: agent.email, type: 'password_reset' },
        {
          secret: process.env.RESET_PASSWORD_SECRET || 'reset-secret',
          expiresIn: '15m', // Token expires in 15 minutes
        },
      );

      // Send password reset email
      await this.sendPasswordResetEmail(agent, resetToken);

      this.logger.info(`Password reset email sent to agent: ${agent.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to reset agent password: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Confirm password reset with token
   */
  async confirmResetPassword(dto: ConfirmResetAgentPasswordDto): Promise<void> {
    try {
      // Verify reset token
      const payload = await this.jwtService.verifyAsync(dto.token, {
        secret: process.env.RESET_PASSWORD_SECRET || 'reset-secret',
      });

      if (payload.type !== 'password_reset') {
        throw new UnauthorizedException('Invalid reset token');
      }

      const agent = await this.findByEmail(payload.email);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Hash new password
      const hashedNewPassword = await this.hashPassword(dto.newPassword);

      // Update password in database
      await this.prisma.agent.update({
        where: { id: agent.id },
        data: { password_hash: hashedNewPassword },
      });

      this.logger.info(`Password reset completed for agent: ${agent.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to confirm password reset: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find agent by ID with admin relationship
   */
  async findById(id: string): Promise<IAgent | null> {
    return await this.prisma.agent.findUnique({
      where: { id },
    });
  }

  /**
   * Find agent by ID with admin relationship (for responses)
   */
  async findByIdWithAdmin(id: string): Promise<IAgentWithAdmin | null> {
    const agent = await this.prisma.agent.findUnique({
      where: { id },
      include: {
        created_by_admin: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!agent) return null;

    const { password_hash, ...agentWithoutPassword } = agent;
    return agentWithoutPassword as IAgentWithAdmin;
  }

  /**
   * Find agent by email
   */
  async findByEmail(email: string): Promise<IAgent | null> {
    return await this.prisma.agent.findUnique({
      where: { email },
    });
  }

  /**
   * Find all agents with filtering and pagination
   */
  async findAll(query: AgentQueryDto): Promise<{
    data: IAgentWithAdmin[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const page = query.page || 1;
      const limit = query.limit || 10;
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (query.status) {
        where.status = query.status;
      }

      if (query.search) {
        where.OR = [
          { name: { contains: query.search, mode: 'insensitive' } },
          { email: { contains: query.search, mode: 'insensitive' } },
        ];
      }

      // Get total count
      const total = await this.prisma.agent.count({ where });

      // Get agents with pagination
      const agents = await this.prisma.agent.findMany({
        where,
        include: {
          created_by_admin: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      });

      // Remove password_hash from results
      const agentsWithoutPassword = agents.map(
        ({ password_hash, ...agent }) => agent,
      ) as IAgentWithAdmin[];

      return {
        data: agentsWithoutPassword,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch agents: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update agent details
   */
  async update(
    id: string,
    dto: UpdateAgentDto,
    updatedBy: string,
  ): Promise<IAgent> {
    try {
      const agent = await this.findById(id);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Check if email is being updated and if it's already taken
      if (dto.email && dto.email !== agent.email) {
        const existingAgent = await this.findByEmail(dto.email);
        if (existingAgent) {
          throw new ConflictException('Email already taken by another agent');
        }
      }

      const updateData: IUpdateAgentData = {};

      if (dto.name) updateData.name = dto.name;
      if (dto.email) updateData.email = dto.email;
      if (dto.phone !== undefined) updateData.phone = dto.phone;
      if (dto.status) updateData.status = dto.status;

      const updatedAgent = await this.prisma.agent.update({
        where: { id },
        data: updateData,
      });

      this.logger.info(
        `Agent updated successfully: ${updatedAgent.email} by: ${updatedBy}`,
      );

      return updatedAgent;
    } catch (error) {
      this.logger.error(
        `Failed to update agent: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Soft delete agent (set status to Inactive)
   */
  async delete(id: string, deletedBy: string): Promise<void> {
    try {
      const agent = await this.findById(id);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Check if agent has active assignments
      const activeAssignments = await this.prisma.application.count({
        where: {
          agent_ids: { has: id },
          status: {
            in: [
              'Draft',
              'Submitted',
              'Under_Review',
              'Additional_Info_Required',
            ],
          },
        },
      });

      if (activeAssignments > 0) {
        throw new BadRequestException(
          `Cannot delete agent with ${activeAssignments} active application assignments. Please reassign applications first.`,
        );
      }

      // Set status to Inactive instead of hard delete
      await this.prisma.agent.update({
        where: { id },
        data: { status: AgentStatus.Inactive },
      });

      this.logger.info(
        `Agent deactivated successfully: ${agent.email} by: ${deletedBy}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to delete agent: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Refresh agent token
   */
  async refreshToken(user: any) {
    const payload: IAgentAuthPayload = {
      id: user.id,
      email: user.email,
      tokenType: 'agent',
      sub: user.sub,
    };

    return {
      accessToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE || '5h',
        secret: process.env.jwtAgentSecretKey,
      }),
      refreshToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE || '7d',
        secret: process.env.jwtRefreshTokenKey,
      }),
      expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
    };
  }

  /**
   * Validate agent credentials
   */
  private async validateAgent(dto: AgentLoginDto): Promise<IAgent> {
    const agent = await this.findByEmail(dto.email);

    if (
      agent &&
      (await this.validatePassword(dto.password, agent.password_hash))
    ) {
      return agent;
    }
    throw new UnauthorizedException('Wrong email or password');
  }

  /**
   * Generate temporary password
   */
  generateTemporaryPassword(): string {
    return randomBytes(8).toString('hex').toUpperCase();
  }

  /**
   * Hash password
   */
  async hashPassword(password: string): Promise<string> {
    return await hash(password, 10);
  }

  /**
   * Validate password
   */
  async validatePassword(password: string, hash: string): Promise<boolean> {
    return await compare(password, hash);
  }

  /**
   * Send welcome email to new agent
   */
  private async sendWelcomeEmail(
    agent: IAgent,
    temporaryPassword: string,
  ): Promise<void> {
    try {
      const loginUrl = `${process.env.WEBSITE}/agent/login`;

      await this.mailerService.sendEmail({
        to: agent.email,
        subject: 'Welcome to CareerIreland - Agent Account Created',
        html: await render(
          AgentWelcomeEmail({
            agentName: agent.name,
            agentEmail: agent.email,
            temporaryPassword,
            loginUrl,
          }),
        ),
        from: process.env.EMAIL || '<EMAIL>',
        cc: [],
      });

      this.logger.info(`Welcome email sent to agent: ${agent.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send welcome email to agent: ${agent.email}`,
        error.stack,
      );
      // Don't throw error here as agent creation should still succeed
    }
  }

  /**
   * Send password reset email to agent
   */
  private async sendPasswordResetEmail(
    agent: IAgent,
    resetToken: string,
  ): Promise<void> {
    try {
      const resetUrl = `${process.env.WEBSITE}/agent/reset-password?token=${resetToken}`;

      await this.mailerService.sendEmail({
        to: agent.email,
        subject: 'CareerIreland Agent - Password Reset',
        html: await render(
          AgentPasswordResetEmail({
            agentName: agent.name,
            resetUrl,
          }),
        ),
        from: process.env.EMAIL || '<EMAIL>',
        cc: [],
      });

      this.logger.info(`Password reset email sent to agent: ${agent.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password reset email to agent: ${agent.email}`,
        error.stack,
      );
      throw error;
    }
  }
}
