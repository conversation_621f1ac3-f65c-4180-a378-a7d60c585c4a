import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../../src/utils/prisma.service';
import { LoggerService } from '../../src/utils/logger.service';
import { MailerService } from '../../src/mailer/mailer.service';
import { NotificationSettingsStorageService } from '../../src/utils/notification-settings-storage.service';
import { ApplicationStatus } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

// Import the service class from the consolidated script
// Note: In a real implementation, you'd extract the class to a separate file for testing
class ConsolidatedDocumentReminderService {
  private readonly logDir = path.join(
    process.cwd(),
    'logs',
    'document-reminders',
  );
  private isSchedulerRunning = false;
  private schedulerTimeout?: NodeJS.Timeout;

  constructor(
    private readonly prisma: PrismaService,
    private readonly loggerService: LoggerService,
    private readonly mailerService: MailerService,
    private readonly notificationSettings: NotificationSettingsStorageService,
  ) {
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  private logToFile(
    level: string,
    message: string,
    context?: string,
    stack?: string,
  ): void {
    // Mock implementation for testing
  }

  async findApplicationsWithMissingDocuments() {
    // Implementation would be here - using mocked data for tests
    return [];
  }

  private async getUserReminderDays(userId?: string): Promise<number> {
    try {
      if (!userId) {
        return 10;
      }
      const settings = await this.notificationSettings.readSettings(userId);
      return settings?.missing_document_reminder_days || 10;
    } catch (error) {
      return 10;
    }
  }

  async sendReminderEmail(application: any): Promise<void> {
    // Mock implementation for testing
  }

  private getRecipientEmail(application: any): string {
    if (application.user?.email) {
      return application.user.email;
    }
    if (application.guest_email) {
      return application.guest_email;
    }
    throw new Error(
      `No email found for application ${application.application_number}`,
    );
  }

  private getRecipientName(application: any): string {
    if (application.user?.name) {
      return application.user.name;
    }
    if (application.guest_name) {
      return application.guest_name;
    }
    return 'User';
  }

  private getServiceDisplayName(serviceType: string): string {
    const serviceNames: Record<string, string> = {
      immigration: 'Immigration Service',
      visa: 'Visa Application Service',
      consultation: 'Consultation Service',
      training: 'Training Service',
      package: 'Service Package',
    };
    return serviceNames[serviceType] || serviceType;
  }

  private getFallbackEmailTemplate(emailData: any): string {
    return '<html><body>Fallback template</body></html>';
  }

  async executeOnce() {
    const startTime = Date.now();
    const result = {
      success: false,
      totalApplicationsChecked: 0,
      remindersEligible: 0,
      remindersSent: 0,
      errors: [],
      executionTime: 0,
    };

    try {
      const applications = await this.findApplicationsWithMissingDocuments();
      result.totalApplicationsChecked = applications.length;
      result.remindersEligible = applications.length;

      for (const application of applications) {
        try {
          await this.sendReminderEmail(application);
          result.remindersSent++;
        } catch (error) {
          result.errors.push(
            `Failed to send reminder for ${application.application_number}: ${error.message}`,
          );
        }
      }

      result.success = result.errors.length === 0;
      result.executionTime = Date.now() - startTime;
      return result;
    } catch (error) {
      result.success = false;
      result.executionTime = Date.now() - startTime;
      result.errors.push(`Script execution failed: ${error.message}`);
      throw error;
    }
  }

  private getSchedulerConfig() {
    return {
      enabled: process.env.SCHEDULER_ENABLED === 'true',
      hour: parseInt(process.env.SCHEDULER_HOUR || '9', 10),
      minute: parseInt(process.env.SCHEDULER_MINUTE || '0', 10),
      timezone: process.env.SCHEDULER_TIMEZONE || 'UTC',
      checkInterval: parseInt(
        process.env.SCHEDULER_CHECK_INTERVAL || '60000',
        10,
      ),
      logLevel: process.env.SCHEDULER_LOG_LEVEL || 'info',
    };
  }

  async startScheduler(): Promise<void> {
    if (this.isSchedulerRunning) {
      return;
    }
    this.isSchedulerRunning = true;
  }

  stopScheduler(): void {
    this.isSchedulerRunning = false;
    if (this.schedulerTimeout) {
      clearTimeout(this.schedulerTimeout);
      this.schedulerTimeout = undefined;
    }
  }

  getSchedulerStatus() {
    return {
      running: this.isSchedulerRunning,
      config: this.getSchedulerConfig(),
    };
  }

  private isScheduledTime(config: any): boolean {
    const now = new Date();
    return now.getHours() === config.hour && now.getMinutes() === config.minute;
  }
}

describe('ConsolidatedDocumentReminderService', () => {
  let service: ConsolidatedDocumentReminderService;
  let prismaService: jest.Mocked<PrismaService>;
  let loggerService: jest.Mocked<LoggerService>;
  let mailerService: jest.Mocked<MailerService>;
  let notificationSettings: jest.Mocked<NotificationSettingsStorageService>;

  beforeEach(async () => {
    const mockPrismaService = {
      application: {
        findMany: jest.fn().mockResolvedValue([]),
      },
    };

    const mockLoggerService = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    };

    const mockMailerService = {
      sendEmail: jest.fn(),
    };

    const mockNotificationSettings = {
      readSettings: jest.fn().mockResolvedValue({
        id: 'test-id',
        user_id: 'user1',
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 10,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: NotificationSettingsStorageService,
          useValue: mockNotificationSettings,
        },
      ],
    }).compile();

    prismaService = module.get(PrismaService);
    loggerService = module.get(LoggerService);
    mailerService = module.get(MailerService);
    notificationSettings = module.get(NotificationSettingsStorageService);

    service = new ConsolidatedDocumentReminderService(
      prismaService,
      loggerService,
      mailerService,
      notificationSettings,
    );
  });

  afterEach(() => {
    service.stopScheduler();
    jest.clearAllMocks();
  });

  describe('Database Query Logic', () => {
    it('should query applications with missing documents using array operators', async () => {
      const mockApplications = [
        {
          id: 'app1',
          application_number: 'APP-001',
          service_type: 'immigration',
          status: ApplicationStatus.Under_Review,
          user_id: 'user1',
          updated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
          user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
          documents: [
            {
              id: 'doc1',
              file_name: 'passport.pdf',
              required: true,
              status: 'pending',
              stage_order: 1,
              request_reason: 'Identity verification',
            },
          ],
        },
      ];

      (prismaService.application.findMany as jest.Mock).mockResolvedValue(
        mockApplications,
      );
      (notificationSettings.readSettings as jest.Mock).mockResolvedValue({
        id: 'test-id',
        user_id: 'user1',
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 10,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const result = await service.findApplicationsWithMissingDocuments();

      expect(prismaService.application.findMany).toHaveBeenCalledWith({
        where: {
          status: {
            not: ApplicationStatus.Completed,
          },
          documents: {
            some: {
              OR: [
                { status: 'pending' },
                { status: 'rejected' },
                { file_url: '' },
              ],
            },
          },
        },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          documents: {
            where: {
              OR: [
                { status: 'pending' },
                { status: 'rejected' },
                { file_url: '' },
              ],
            },
            orderBy: { stage_order: 'asc' },
          },
        },
        orderBy: { updated_at: 'desc' },
      });
    });

    it('should handle database query errors gracefully', async () => {
      const error = new Error('Database connection failed');
      (prismaService.application.findMany as jest.Mock).mockRejectedValue(
        error,
      );

      await expect(
        service.findApplicationsWithMissingDocuments(),
      ).rejects.toThrow('Database connection failed');
    });
  });

  describe('Date Logic and Reminder Timing', () => {
    it('should calculate days since last update correctly', async () => {
      const tenDaysAgo = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000);
      const mockApplications = [
        {
          id: 'app1',
          application_number: 'APP-001',
          service_type: 'immigration',
          status: ApplicationStatus.Under_Review,
          user_id: 'user1',
          updated_at: tenDaysAgo,
          user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
          documents: [
            {
              id: 'doc1',
              file_name: 'passport.pdf',
              required: true,
              status: 'pending',
              stage_order: 1,
            },
          ],
        },
      ];

      (prismaService.application.findMany as jest.Mock).mockResolvedValue(
        mockApplications,
      );
      (notificationSettings.readSettings as jest.Mock).mockResolvedValue({
        id: 'settings-id',
        user_id: 'user1',
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 10,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: '2025-07-19T00:00:00.000Z',
        updated_at: '2025-07-19T00:00:00.000Z',
      });

      const result = await service.findApplicationsWithMissingDocuments();

      // Should include application since it matches the 10-day reminder interval
      expect(result).toHaveLength(1);
      expect(result[0].daysSinceLastUpdate).toBe(10);
    });

    it('should exclude applications outside reminder window', async () => {
      const fiveDaysAgo = new Date(Date.now() - 5 * 24 * 60 * 60 * 1000);
      const mockApplications = [
        {
          id: 'app1',
          application_number: 'APP-001',
          service_type: 'immigration',
          status: ApplicationStatus.Under_Review,
          user_id: 'user1',
          updated_at: fiveDaysAgo,
          user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
          documents: [
            {
              id: 'doc1',
              file_name: 'passport.pdf',
              required: true,
              status: 'pending',
              stage_order: 1,
            },
          ],
        },
      ];

      (prismaService.application.findMany as jest.Mock).mockResolvedValue(
        mockApplications,
      );
      (notificationSettings.readSettings as jest.Mock).mockResolvedValue({
        id: 'settings-id',
        user_id: 'user1',
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 10,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: '2025-07-19T00:00:00.000Z',
        updated_at: '2025-07-19T00:00:00.000Z',
      });

      const result = await service.findApplicationsWithMissingDocuments();

      // Should exclude application since it doesn't match the 10-day reminder interval
      expect(result).toHaveLength(0);
    });

    it('should exclude applications older than 30 days', async () => {
      const thirtyFiveDaysAgo = new Date(Date.now() - 35 * 24 * 60 * 60 * 1000);
      const mockApplications = [
        {
          id: 'app1',
          application_number: 'APP-001',
          service_type: 'immigration',
          status: ApplicationStatus.Under_Review,
          user_id: 'user1',
          updated_at: thirtyFiveDaysAgo,
          user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
          documents: [
            {
              id: 'doc1',
              file_name: 'passport.pdf',
              required: true,
              status: 'pending',
              stage_order: 1,
            },
          ],
        },
      ];

      (prismaService.application.findMany as jest.Mock).mockResolvedValue(
        mockApplications,
      );
      (notificationSettings.readSettings as jest.Mock).mockResolvedValue({
        id: 'settings-id',
        user_id: 'user1',
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 35,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: '2025-07-19T00:00:00.000Z',
        updated_at: '2025-07-19T00:00:00.000Z',
      });

      const result = await service.findApplicationsWithMissingDocuments();

      // Should exclude application since it's older than 30 days
      expect(result).toHaveLength(0);
    });
  });

  describe('Email Functionality', () => {
    it('should send email with correct data structure', async () => {
      const mockApplication = {
        id: 'app1',
        application_number: 'APP-001',
        service_type: 'immigration',
        user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
        missingDocuments: [
          {
            file_name: 'passport.pdf',
            required: true,
            status: 'pending',
            request_reason: 'Identity verification',
          },
        ],
        daysSinceLastUpdate: 10,
      };

      (mailerService.sendEmail as jest.Mock).mockResolvedValue({
        id: 'email-123',
      });

      await service.sendReminderEmail(mockApplication);

      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: expect.stringMatching(
            /Document Reminder - APP-001.*Complete within 7 days/,
          ),
          html: expect.any(String),
        }),
      );
    });

    it('should use fallback template when React email fails', async () => {
      const mockApplication = {
        id: 'app1',
        application_number: 'APP-001',
        service_type: 'immigration',
        user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
        missingDocuments: [
          {
            file_name: 'passport.pdf',
            required: true,
            status: 'pending',
            request_reason: 'Identity verification',
          },
        ],
        daysSinceLastUpdate: 10,
      };

      // Mock React email render to fail first, then succeed with fallback
      (mailerService.sendEmail as jest.Mock)
        .mockRejectedValueOnce(new Error('React email render failed'))
        .mockResolvedValueOnce({ id: 'email-123' });

      await service.sendReminderEmail(mockApplication);

      // Should be called twice - once for React email (fails), once for fallback
      expect(mailerService.sendEmail).toHaveBeenCalledTimes(2);
    });

    it('should handle guest user emails correctly', async () => {
      const mockApplication = {
        id: 'app1',
        application_number: 'APP-001',
        service_type: 'immigration',
        guest_name: 'Jane Guest',
        guest_email: '<EMAIL>',
        missingDocuments: [
          {
            file_name: 'passport.pdf',
            required: true,
            status: 'pending',
          },
        ],
        daysSinceLastUpdate: 10,
      };

      (mailerService.sendEmail as jest.Mock).mockResolvedValue({
        id: 'email-123',
      });

      await service.sendReminderEmail(mockApplication);

      expect(mailerService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
      );
    });

    it('should throw error when no email is available', async () => {
      const mockApplication = {
        id: 'app1',
        application_number: 'APP-001',
        service_type: 'immigration',
        missingDocuments: [],
        daysSinceLastUpdate: 10,
      };

      await expect(service.sendReminderEmail(mockApplication)).rejects.toThrow(
        'No email found for application APP-001',
      );
    });
  });

  describe('Error Scenarios and Edge Cases', () => {
    it('should handle notification settings read failure gracefully', async () => {
      notificationSettings.readSettings.mockRejectedValue(
        new Error('Settings not found'),
      );

      const result = await service['getUserReminderDays']('user1');

      expect(result).toBe(10); // Should return default value
    });

    it('should handle missing user ID gracefully', async () => {
      const result = await service['getUserReminderDays'](undefined);

      expect(result).toBe(10); // Should return default value for guest users
    });

    it('should handle email sending failures in executeOnce', async () => {
      const mockApplications = [
        {
          id: 'app1',
          application_number: 'APP-001',
          service_type: 'immigration',
          user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
          missingDocuments: [
            {
              file_name: 'passport.pdf',
              required: true,
              status: 'pending',
            },
          ],
          daysSinceLastUpdate: 10,
        },
      ];

      // Mock the findApplicationsWithMissingDocuments method
      jest
        .spyOn(service, 'findApplicationsWithMissingDocuments')
        .mockResolvedValue(mockApplications);

      // Mock email sending to fail
      jest
        .spyOn(service, 'sendReminderEmail')
        .mockRejectedValue(new Error('Email service unavailable'));

      const result = await service.executeOnce();

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('Failed to send reminder for APP-001');
      expect(result.remindersSent).toBe(0);
      expect(result.totalApplicationsChecked).toBe(1);
    });

    it('should handle complete execution failure', async () => {
      jest
        .spyOn(service, 'findApplicationsWithMissingDocuments')
        .mockRejectedValue(new Error('Database error'));

      await expect(service.executeOnce()).rejects.toThrow('Database error');
    });

    it('should get correct service display names', async () => {
      expect(service['getServiceDisplayName']('immigration')).toBe(
        'Immigration Service',
      );
      expect(service['getServiceDisplayName']('visa')).toBe(
        'Visa Application Service',
      );
      expect(service['getServiceDisplayName']('consultation')).toBe(
        'Consultation Service',
      );
      expect(service['getServiceDisplayName']('training')).toBe(
        'Training Service',
      );
      expect(service['getServiceDisplayName']('package')).toBe(
        'Service Package',
      );
      expect(service['getServiceDisplayName']('unknown')).toBe('unknown');
    });

    it('should get correct recipient names', async () => {
      const userApp = {
        user: { name: 'John Doe' },
      };
      const guestApp = {
        guest_name: 'Jane Guest',
      };
      const noNameApp = {};

      expect(service['getRecipientName'](userApp)).toBe('John Doe');
      expect(service['getRecipientName'](guestApp)).toBe('Jane Guest');
      expect(service['getRecipientName'](noNameApp)).toBe('User');
    });
  });

  describe('Scheduler Functionality', () => {
    it('should start scheduler successfully', async () => {
      await service.startScheduler();

      const status = service.getSchedulerStatus();
      expect(status.running).toBe(true);
    });

    it('should not start scheduler if already running', async () => {
      await service.startScheduler();
      await service.startScheduler(); // Second call should be ignored

      const status = service.getSchedulerStatus();
      expect(status.running).toBe(true);
    });

    it('should stop scheduler successfully', async () => {
      await service.startScheduler();
      service.stopScheduler();

      const status = service.getSchedulerStatus();
      expect(status.running).toBe(false);
    });

    it('should get correct scheduler configuration', async () => {
      process.env.SCHEDULER_ENABLED = 'true';
      process.env.SCHEDULER_HOUR = '14';
      process.env.SCHEDULER_MINUTE = '30';
      process.env.SCHEDULER_TIMEZONE = 'America/New_York';
      process.env.SCHEDULER_CHECK_INTERVAL = '30000';
      process.env.SCHEDULER_LOG_LEVEL = 'debug';

      const status = service.getSchedulerStatus();

      expect(status.config.enabled).toBe(true);
      expect(status.config.hour).toBe(14);
      expect(status.config.minute).toBe(30);
      expect(status.config.timezone).toBe('America/New_York');
      expect(status.config.checkInterval).toBe(30000);
      expect(status.config.logLevel).toBe('debug');

      // Clean up
      delete process.env.SCHEDULER_ENABLED;
      delete process.env.SCHEDULER_HOUR;
      delete process.env.SCHEDULER_MINUTE;
      delete process.env.SCHEDULER_TIMEZONE;
      delete process.env.SCHEDULER_CHECK_INTERVAL;
      delete process.env.SCHEDULER_LOG_LEVEL;
    });

    it('should use default scheduler configuration when env vars not set', async () => {
      const status = service.getSchedulerStatus();

      expect(status.config.enabled).toBe(false);
      expect(status.config.hour).toBe(9);
      expect(status.config.minute).toBe(0);
      expect(status.config.timezone).toBe('UTC');
      expect(status.config.checkInterval).toBe(60000);
      expect(status.config.logLevel).toBe('info');
    });

    it('should correctly identify scheduled time', async () => {
      const config = {
        hour: 9,
        minute: 30,
      };

      // Mock current time to match scheduled time
      const mockDate = new Date();
      mockDate.setHours(9, 30, 0, 0);
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

      const isScheduledTime = service['isScheduledTime'](config);
      expect(isScheduledTime).toBe(true);

      // Mock current time to not match scheduled time
      mockDate.setHours(10, 30, 0, 0);
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate);

      const isNotScheduledTime = service['isScheduledTime'](config);
      expect(isNotScheduledTime).toBe(false);

      // Restore Date
      jest.restoreAllMocks();
    });
  });

  describe('File Logging', () => {
    it('should create log directory if it does not exist', async () => {
      const logDir = path.join(process.cwd(), 'logs', 'document-reminders');

      // Remove directory if it exists
      if (fs.existsSync(logDir)) {
        fs.rmSync(logDir, { recursive: true });
      }

      // Create new service instance which should create the directory
      const newService = new ConsolidatedDocumentReminderService(
        prismaService,
        loggerService,
        mailerService,
        notificationSettings,
      );

      expect(fs.existsSync(logDir)).toBe(true);

      // Clean up
      newService.stopScheduler();
    });

    it('should handle log file writing errors gracefully', async () => {
      // Mock fs.appendFileSync to throw an error
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const appendFileSyncSpy = jest
        .spyOn(fs, 'appendFileSync')
        .mockImplementation(() => {
          throw new Error('Permission denied');
        });

      service['logToFile']('info', 'Test message');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to write to log file:',
        expect.any(Error),
      );

      // Restore
      appendFileSyncSpy.mockRestore();
      consoleSpy.mockRestore();
    });
  });

  describe('Integration Tests', () => {
    it('should execute complete workflow successfully', async () => {
      const mockApplications = [
        {
          id: 'app1',
          application_number: 'APP-001',
          service_type: 'immigration',
          status: ApplicationStatus.Under_Review,
          user_id: 'user1',
          updated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          user: { id: 'user1', name: 'John Doe', email: '<EMAIL>' },
          documents: [
            {
              id: 'doc1',
              file_name: 'passport.pdf',
              required: true,
              status: 'pending',
              stage_order: 1,
              request_reason: 'Identity verification',
            },
          ],
        },
      ];

      (prismaService.application.findMany as jest.Mock).mockResolvedValue(
        mockApplications,
      );
      (notificationSettings.readSettings as jest.Mock).mockResolvedValue({
        id: 'settings-id',
        user_id: 'user1',
        agent_assigned: true,
        case_status_update: true,
        agent_query: true,
        document_rejection: true,
        missing_document_reminder_days: 10,
        system_maintenance: true,
        final_decision_issued: true,
        created_at: '2025-07-19T00:00:00.000Z',
        updated_at: '2025-07-19T00:00:00.000Z',
      });
      (mailerService.sendEmail as jest.Mock).mockResolvedValue({
        id: 'email-123',
      });

      const result = await service.executeOnce();

      expect(result.success).toBe(true);
      expect(result.totalApplicationsChecked).toBe(1);
      expect(result.remindersSent).toBe(1);
      expect(result.errors).toHaveLength(0);
      expect(result.executionTime).toBeGreaterThan(0);
    });
  });
});
