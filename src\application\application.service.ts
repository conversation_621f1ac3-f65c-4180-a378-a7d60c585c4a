/**
 * Simplified Application Service
 *
 * Consolidated service for all application-related operations.
 * Replaces complex abstractions with a clean, maintainable implementation.
 *
 * Features:
 * - Application creation from payments
 * - Application retrieval with filtering
 * - Role-based access control
 * - Simple workflow integration
 * - Comprehensive error handling
 */

import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { PrismaService } from '../utils/prisma.service';
import { ApplicationStatus, PriorityLevel, DocumentType } from '@prisma/client';
import {
  ApplicationFormService,
  StageFieldUpdate,
} from './services/application-form.service';
import { ApplicationDocumentService } from './services/application-document.service';
import { ApplicationTransformerService } from './services/application-transformer.service';
import { EmailTemplateIntegrationService } from './services/email-template-integration.service';
import { MediaService } from '../media/media.service';
import { DocumentVaultService } from './services/document-vault.service';
import { NotificationService } from './services/notification.service';
import {
  UpdateDocumentStatusDto,
  RequestDocumentDto,
  DocumentStatusEnum,
} from './dto/document-status.dto';
import {
  CreateNewApplicationDto,
  AssignWorkflowTemplateDto,
  AssignWorkflowTemplateResponseDto,
} from './dto/application.dto';
import {
  UpdateApplicationStatusDto,
  UpdateApplicationStatusResponseDto,
} from './dto/update-application.dto';
import { NotificationSettingsStorageService } from '../utils/notification-settings-storage.service';
import { LoggerService } from '../utils/logger.service';

export interface CreateApplicationData {
  paymentId: string;
  serviceType: string;
  serviceId: string;
  workflowTemplateId?: string;
  userId?: string;
  guestName?: string;
  guestEmail?: string;
  guestMobile?: string;
}

export interface ApplicationFilters {
  service_type?: string;
  status?: ApplicationStatus;
  user_id?: string;
  assigned_to?: string;
  priority_level?: PriorityLevel;
  created_from?: string;
  created_to?: string;
}

@Injectable()
export class ApplicationService {
  private readonly logger = new Logger(ApplicationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly formService: ApplicationFormService,
    private readonly documentService: ApplicationDocumentService,
    private readonly transformerService: ApplicationTransformerService,
    private readonly emailTemplateService: EmailTemplateIntegrationService,
    private readonly mediaService: MediaService,
    private readonly documentVaultService: DocumentVaultService,
    private readonly notificationService: NotificationService,
    private readonly notificationSettingsStorage: NotificationSettingsStorageService,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Create application from payment data
   * Called from payment webhook after successful payment
   */
  async createApplicationFromPayment(
    data: CreateApplicationData,
  ): Promise<any> {
    try {
      this.logger.log(`Creating application from payment: ${data.paymentId}`);

      // Check if application already exists for this payment
      const existingApplication = await this.prisma.application.findFirst({
        where: { payment_ids: { has: data.paymentId } },
      });

      if (existingApplication) {
        this.logger.warn(
          `Application already exists for payment: ${data.paymentId}`,
        );
        return existingApplication;
      }

      // Generate unique application number
      const applicationNumber = await this.generateApplicationNumber(
        data.serviceType,
      );

      // Prepare application data
      const applicationData = {
        application_number: applicationNumber,
        service_type: data.serviceType,
        service_id: data.serviceId,
        workflow_template_id: data.workflowTemplateId,
        payment_ids: [data.paymentId], // Store as array
        agent_ids: [], // Initialize empty array
        current_step: '1', // Start at step 1
        status: ApplicationStatus.Pending, // Set to Pending for payment webhook created applications
        priority_level: PriorityLevel.Medium,
        created_by: data.userId || 'system',
        steps: {}, // Initialize with empty steps object
      };

      // Add user or guest information
      if (data.userId) {
        Object.assign(applicationData, {
          user_id: data.userId,
        });
      } else {
        Object.assign(applicationData, {
          guest_name: data.guestName,
          guest_email: data.guestEmail,
          guest_mobile: data.guestMobile,
        });
      }

      // Create application record
      const application = await this.prisma.application.create({
        data: applicationData as any,
      });

      // Populate workflow template data if available
      if (data.workflowTemplateId) {
        try {
          const workflowTemplate =
            await this.prisma.workflow_template.findUnique({
              where: { id: data.workflowTemplateId },
              select: { workflowTemplate: true },
            });

          if (workflowTemplate?.workflowTemplate) {
            const templateData = workflowTemplate.workflowTemplate as any[];

            // Populate form data from workflow template
            await this.formService.populateFormDataFromTemplate(
              application.id,
              templateData,
            );

            // Populate document requirements from workflow template
            await this.documentService.populateDocumentRequirementsFromTemplate(
              application.id,
              templateData,
            );

            this.logger.log(
              `Successfully populated workflow template data for application: ${application.id}`,
            );
          }
        } catch (templateError) {
          this.logger.warn(
            `Failed to populate workflow template data for application: ${application.id}`,
            templateError,
          );
          // Don't fail the entire application creation if template population fails
        }
      }

      this.logger.log(
        `Application created successfully: ${application.id} (${application.application_number})`,
      );

      // Send application requirements email
      await this.sendApplicationCreationEmail(application.id);

      return application;
    } catch (error) {
      this.logger.error(
        `Failed to create application from payment: ${data.paymentId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Create a new application with multiple payments and agents support
   * Called from the POST /applications endpoint
   * Auto-assigns agent users to their created applications
   */
  async createNewApplication(
    dto: CreateNewApplicationDto,
    createdBy: string,
    userTokenType?: string,
  ): Promise<any> {
    try {
      this.logger.log(`Creating new application for user: ${createdBy}`);

      // Validate that payment IDs exist
      if (dto.payments && dto.payments.length > 0) {
        const existingPayments = await this.prisma.payment.findMany({
          where: { id: { in: dto.payments } },
          select: { id: true },
        });

        if (existingPayments.length !== dto.payments.length) {
          throw new Error('One or more payment IDs are invalid');
        }
      }

      // Validate workflow template exists
      if (dto.workflow_template_id) {
        const workflowTemplate = await this.prisma.workflow_template.findUnique(
          {
            where: { id: dto.workflow_template_id },
            select: { id: true },
          },
        );

        if (!workflowTemplate) {
          throw new Error('Invalid workflow template ID');
        }
      }

      // Generate unique application number
      const applicationNumber = await this.generateApplicationNumber(
        dto.service_type,
      );

      // Prepare agent_ids with auto-assignment logic
      let agentIds = dto.assigned_agent || [];

      // Auto-assign agent users to their created applications
      if (userTokenType === 'agent') {
        // Add the creating agent to agent_ids if not already present
        if (!agentIds.includes(createdBy)) {
          agentIds = [...agentIds, createdBy];
        }
        this.logger.log(`Auto-assigned agent ${createdBy} to application`);
      } else if (userTokenType === 'admin') {
        // Admin users are not auto-assigned, preserve existing agent_ids
        this.logger.log(
          `Admin user ${createdBy} created application - no auto-assignment`,
        );
      }

      // Prepare application data with array support
      const applicationData = {
        application_number: applicationNumber,
        service_type: dto.service_type,
        service_id: dto.service_id,
        user_id: dto.user_id,
        workflow_template_id: dto.workflow_template_id,
        payment_ids: dto.payments || [],
        agent_ids: agentIds,
        current_step: '1', // Start at step 1
        status: ApplicationStatus.Pending, // Set to Pending for POST /applications endpoint
        priority_level: dto.priority_level,
        created_by: createdBy,
        steps: {}, // Initialize with empty steps object
      };

      // Create the application
      const application = await this.prisma.application.create({
        data: applicationData,
        include: {
          user: { select: { id: true, name: true, email: true } },
          workflow_template: {
            select: {
              id: true,
              name: true,
              description: true,
              workflowTemplate: true,
            },
          },
        },
      });

      // Populate workflow template data if available
      if (dto.workflow_template_id) {
        try {
          const workflowTemplate =
            await this.prisma.workflow_template.findUnique({
              where: { id: dto.workflow_template_id },
              select: { workflowTemplate: true },
            });

          if (workflowTemplate?.workflowTemplate) {
            const templateData = workflowTemplate.workflowTemplate as any[];

            // Populate form data from workflow template
            await this.formService.populateFormDataFromTemplate(
              application.id,
              templateData,
            );

            // Populate document requirements from workflow template
            await this.documentService.populateDocumentRequirementsFromTemplate(
              application.id,
              templateData,
            );

            this.logger.log(
              `Successfully populated workflow template data for application: ${application.id}`,
            );
          }
        } catch (templateError) {
          this.logger.warn(
            `Failed to populate workflow template data for application: ${application.id}`,
            templateError,
          );
          // Don't fail the entire application creation if template population fails
        }
      }

      this.logger.log(
        `Application created successfully: ${application.id} (${application.application_number})`,
      );

      // Send application requirements email
      await this.sendApplicationCreationEmail(application.id);

      return {
        id: application.id,
        application_number: application.application_number,
        service_type: application.service_type,
        service_id: application.service_id,
        user_id: application.user_id,
        priority_level: application.priority_level,
        workflow_template_id: application.workflow_template_id,
        payment_ids: application.payment_ids,
        agent_ids: application.agent_ids,
        status: application.status,
        current_step: application.current_step,
        created_at: application.created_at,
        updated_at: application.updated_at,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create new application: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get applications with filtering and pagination
   */
  async getApplications(
    filters: ApplicationFilters = {},
    page = 1,
    limit = 10,
  ) {
    try {
      const skip = (page - 1) * limit;
      const where: any = {};

      // Apply filters
      if (filters.service_type) where.service_type = filters.service_type;
      if (filters.status) where.status = filters.status;
      if (filters.user_id) where.user_id = filters.user_id;
      if (filters.assigned_to) where.agent_ids = { has: filters.assigned_to };
      if (filters.priority_level) where.priority_level = filters.priority_level;

      // Date range filters
      if (filters.created_from || filters.created_to) {
        where.created_at = {};
        if (filters.created_from)
          where.created_at.gte = new Date(filters.created_from);
        if (filters.created_to)
          where.created_at.lte = new Date(filters.created_to);
      }

      const [applications, total] = await Promise.all([
        this.prisma.application.findMany({
          where,
          include: {
            user: { select: { id: true, name: true, email: true } },
          },
          orderBy: { created_at: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.application.count({ where }),
      ]);

      return {
        applications,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(
        `Failed to get applications: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get application details by ID with complete data structure
   */
  async getApplicationById(applicationId: string): Promise<any> {
    try {
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        include: {
          user: { select: { id: true, name: true, email: true } },
          workflow_template: {
            select: {
              id: true,
              name: true,
              description: true,
              workflowTemplate: true,
            },
          },
          form_data: {
            orderBy: { created_at: 'asc' },
          },
          documents: {
            include: {
              document: {
                select: {
                  id: true,
                  document_name: true,
                  original_filename: true,
                  file_path: true,
                  created_at: true,
                },
              },
            },
            orderBy: { updated_at: 'desc' },
          },
        },
      });

      if (!application) {
        throw new NotFoundException(`Application not found: ${applicationId}`);
      }

      // Fetch payments separately since they're now in an array
      let payments = [];
      if (application.payment_ids && application.payment_ids.length > 0) {
        payments = await this.prisma.payment.findMany({
          where: { id: { in: application.payment_ids } },
          select: { id: true, amount: true, status: true },
        });
      }

      // Fetch agents separately since they're now in an array
      let agents = [];
      if (application.agent_ids && application.agent_ids.length > 0) {
        agents = await this.prisma.agent.findMany({
          where: { id: { in: application.agent_ids } },
          select: { id: true, name: true, email: true },
        });
      }

      // Resolve service name based on service_type and service_id
      const serviceResult = await this.resolveServiceName(
        application.service_type,
        application.service_id,
      );

      const serviceName =
        typeof serviceResult === 'string'
          ? serviceResult
          : serviceResult.serviceName;

      // Add service name, payments, and agents to application data
      const applicationWithServiceName = {
        ...application,
        service_name: serviceName,
        payments: payments,
        agents: agents,
      };

      // Transform the application data using the transformer service
      return this.transformerService.transformApplicationDetails(
        applicationWithServiceName,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to get application details: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate user access to application
   */
  async validateUserAccess(
    applicationId: string,
    userId: string,
  ): Promise<boolean> {
    try {
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: { user_id: true },
      });

      return Boolean(application && application.user_id === userId);
    } catch (error) {
      this.logger.error(`Failed to validate user access: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate unique application number
   */
  private async generateApplicationNumber(
    serviceType: string,
  ): Promise<string> {
    try {
      const year = new Date().getFullYear();
      const prefix = this.getApplicationPrefix(serviceType);
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        // Get the count of applications for this service type and year
        const count = await this.prisma.application.count({
          where: {
            service_type: serviceType,
            created_at: {
              gte: new Date(`${year}-01-01`),
              lt: new Date(`${year + 1}-01-01`),
            },
          },
        });

        const sequenceNumber = (count + 1 + attempts)
          .toString()
          .padStart(6, '0');
        const applicationNumber = `${prefix}-${year}-${sequenceNumber}`;

        // Check if this number already exists
        const existingApplication = await this.prisma.application.findFirst({
          where: { application_number: applicationNumber },
        });

        if (!existingApplication) {
          this.logger.log(`Generated application number: ${applicationNumber}`);
          return applicationNumber;
        }

        attempts++;
      }

      // Fallback with timestamp if all attempts fail
      const timestamp = Date.now().toString().slice(-6);
      const fallbackNumber = `${prefix}-${year}-${timestamp}`;

      this.logger.warn(`Used fallback application number: ${fallbackNumber}`);
      return fallbackNumber;
    } catch (error) {
      this.logger.error('Failed to generate application number', error);
      // Return a fallback number to prevent payment flow disruption
      const timestamp = Date.now().toString().slice(-6);
      return `${this.getApplicationPrefix(serviceType)}-${new Date().getFullYear()}-${timestamp}`;
    }
  }

  /**
   * Update application form data
   */
  async updateApplicationFormData(
    applicationId: string,
    formUpdates: StageFieldUpdate[],
    currentStep?: string,
  ): Promise<any> {
    try {
      this.logger.log(`Updating application form data: ${applicationId}`);

      // Update form fields
      await this.formService.updateFormFields(applicationId, formUpdates);

      // Update current step if provided
      if (currentStep) {
        await this.prisma.application.update({
          where: { id: applicationId },
          data: {
            current_step: currentStep,
            updated_at: new Date(),
          },
        });
      }

      // Return updated application details
      return this.getApplicationById(applicationId);
    } catch (error) {
      this.logger.error(
        `Failed to update application form data: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update only the current step of an application
   */
  async updateCurrentStep(
    applicationId: string,
    currentStep: string,
  ): Promise<{ applicationId: string; currentStep: string }> {
    try {
      this.logger.log(
        `Updating current step for application: ${applicationId} to step: ${currentStep}`,
      );

      // Verify application exists and get workflow template
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: {
          id: true,
          current_step: true,
          workflow_template: {
            select: {
              workflowTemplate: true,
            },
          },
        },
      });

      if (!application) {
        throw new NotFoundException(`Application not found: ${applicationId}`);
      }

      // Validate current step against workflow template
      const workflowTemplate = application.workflow_template?.workflowTemplate;

      if (Array.isArray(workflowTemplate)) {
        const totalSteps = workflowTemplate.length;
        const requestedStep = parseInt(currentStep, 10);

        if (!isNaN(requestedStep) && requestedStep > totalSteps) {
          throw new BadRequestException(
            `Cannot update to step ${currentStep}. Total workflow steps (${totalSteps}) is greater than requested step.`,
          );
        }
      }

      // Update current step
      await this.prisma.application.update({
        where: { id: applicationId },
        data: {
          current_step: currentStep,
          updated_at: new Date(),
        },
      });

      this.logger.log(
        `Successfully updated current step for application: ${applicationId} from ${application.current_step} to ${currentStep}`,
      );

      return {
        applicationId,
        currentStep,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to update current step for application: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update application estimated completion date
   */
  async updateEstimatedCompletion(
    applicationId: string,
    estimatedCompletion: string,
  ): Promise<any> {
    try {
      this.logger.log(
        `Updating estimated completion for application: ${applicationId}`,
      );

      // Validate that the application exists
      const existingApplication = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: { id: true, application_number: true },
      });

      if (!existingApplication) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }

      // Parse and validate the date
      const completionDate = new Date(estimatedCompletion);
      if (isNaN(completionDate.getTime())) {
        throw new HttpException('Invalid date format', HttpStatus.BAD_REQUEST);
      }

      // Ensure the date is in the future
      const now = new Date();
      if (completionDate <= now) {
        throw new HttpException(
          'Estimated completion date must be in the future',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Update the estimated completion date
      await this.prisma.application.update({
        where: { id: applicationId },
        data: {
          estimated_completion: completionDate,
          updated_at: new Date(),
        },
      });

      this.logger.log(
        `Successfully updated estimated completion for application: ${applicationId} to ${completionDate.toISOString()}`,
      );

      // Return updated application details
      return this.getApplicationById(applicationId);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update estimated completion for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update estimated completion date',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update application note
   */
  async updateApplicationNote(
    applicationId: string,
    note: string,
  ): Promise<any> {
    try {
      this.logger.log(`Updating note for application: ${applicationId}`);

      // Validate that the application exists
      const existingApplication = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: { id: true, application_number: true },
      });

      if (!existingApplication) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }

      // Update the application note
      const updatedApplication = await this.prisma.application.update({
        where: { id: applicationId },
        data: {
          note: note,
          updated_at: new Date(),
        },
        select: {
          id: true,
          note: true,
          updated_at: true,
        },
      });

      this.logger.log(
        `Successfully updated note for application: ${applicationId}`,
      );

      return updatedApplication;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(
        `Failed to update note for application: ${applicationId}`,
        error,
      );
      throw new HttpException(
        'Failed to update application note',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Upload document for application
   */
  async uploadApplicationDocument(
    applicationId: string,
    file: Express.Multer.File,
    metadata: {
      document_id: string;
      document_name: string;
      document_category?: string;
      stage_order: number;
      required?: boolean;
      expiry_date?: string;
    },
    userId?: string,
  ): Promise<any> {
    try {
      this.logger.log(`Uploading document for application: ${applicationId}`);

      // Check if document_id already exists in application_document table
      const existingApplicationDocument =
        await this.prisma.application_document.findFirst({
          where: {
            application_id: applicationId,
            document_vault_id: metadata.document_id,
          },
        });

      if (existingApplicationDocument) {
        this.logger.log(
          `Document ${metadata.document_id} already exists for application: ${applicationId}`,
        );
        return {
          document_id: existingApplicationDocument.document_vault_id,
          document_name: existingApplicationDocument.file_name,
          file_path: existingApplicationDocument.file_url,
          file_size: 0, // Size not stored in application_document
          upload_date: existingApplicationDocument.upload_date.toISOString(),
          status: existingApplicationDocument.status,
        };
      }

      // Validate application exists and user has access
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: {
          id: true,
          user_id: true,
          guest_email: true,
          agent_ids: true,
        },
      });

      if (!application) {
        throw new NotFoundException(`Application not found: ${applicationId}`);
      }

      // First, check if there's already an application_document record for this application, stage, and document name
      // This is the key fix - check BEFORE creating a new document in the vault
      const existingDocumentRecord =
        await this.prisma.application_document.findFirst({
          where: {
            application_id: applicationId,
            stage_order: metadata.stage_order,
            file_name: metadata.document_name,
          },
        });

      // Check if the provided document_id exists in document_vault
      const existingVaultDocument = await this.prisma.document_vault.findUnique(
        {
          where: { id: metadata.document_id },
        },
      );

      let uploadedDocument: any;

      if (existingVaultDocument) {
        // Use existing document from vault
        this.logger.log(
          `Using existing document from vault: ${metadata.document_id}`,
        );
        uploadedDocument = existingVaultDocument;
      } else {
        // Prepare document metadata for vault service
        const documentMetadata = {
          document_name: metadata.document_name,
          document_type: 'Other' as DocumentType, // Default document type since it's no longer required
          document_category: metadata.document_category,
          expiry_date: metadata.expiry_date
            ? new Date(metadata.expiry_date)
            : undefined,
          application_id: applicationId,
          guest_email: application.guest_email,
        };

        // Upload document to vault using enhanced application-specific structure
        uploadedDocument =
          await this.documentVaultService.uploadApplicationDocument(
            file,
            applicationId,
            documentMetadata,
            userId || application.user_id,
          );
      }

      if (existingDocumentRecord) {
        // Document record already exists for this application/stage/name combination
        // Update the existing record with the new document information
        this.logger.log(
          `Updating existing document record: ${existingDocumentRecord.id} for application: ${applicationId}`,
        );

        await this.prisma.application_document.update({
          where: { id: existingDocumentRecord.id },
          data: {
            document_vault_id: uploadedDocument.id,
            file_url: uploadedDocument.file_path,
            required: metadata.required || false,
            status: 'uploaded',
            uploaded_by: userId || application.user_id,
            upload_date: new Date(),
            submitted_at: new Date(),
            updated_at: new Date(),
          },
        });

        this.logger.log(
          `Successfully updated existing document record: ${existingDocumentRecord.id}`,
        );
      } else {
        // No existing record found, create a new application_document record
        this.logger.log(
          `Creating new document record for application: ${applicationId}, stage: ${metadata.stage_order}, document: ${metadata.document_name}`,
        );

        await this.prisma.application_document.create({
          data: {
            application_id: applicationId,
            document_vault_id: existingVaultDocument
              ? metadata.document_id
              : uploadedDocument.id,
            stage_order: metadata.stage_order,
            file_name: metadata.document_name,
            file_url: uploadedDocument.file_path,
            required: metadata.required || false,
            status: 'uploaded',
            request_reason: `Document for stage ${metadata.stage_order}`,
            uploaded_by: userId || application.user_id,
            upload_date: new Date(),
            submitted_at: new Date(),
          },
        });

        this.logger.log(
          `Successfully created new document record for application: ${applicationId}`,
        );
      }

      this.logger.log(
        `Document uploaded successfully for application: ${applicationId}, document: ${uploadedDocument.id}`,
      );

      return {
        document_id: existingVaultDocument
          ? metadata.document_id
          : uploadedDocument.id,
        document_name: uploadedDocument.document_name,
        file_path: uploadedDocument.file_path,
        file_size: uploadedDocument.file_size,
        upload_date: uploadedDocument.created_at.toISOString(),
        status: 'uploaded',
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(
        `Failed to upload document for application: ${applicationId}`,
        error,
      );
      throw new BadRequestException('Failed to upload document');
    }
  }

  /**
   * Get applications with enhanced filtering and transformation
   */
  async getApplicationsWithTransformation(
    filters: ApplicationFilters = {},
    page = 1,
    limit = 10,
    search?: string,
  ) {
    try {
      const skip = (page - 1) * limit;
      const where: any = {};

      // Apply filters
      if (filters.service_type) where.service_type = filters.service_type;
      if (filters.status) where.status = filters.status;
      if (filters.user_id) where.user_id = filters.user_id;
      if (filters.assigned_to) where.agent_ids = { has: filters.assigned_to };
      if (filters.priority_level) where.priority_level = filters.priority_level;

      // Date range filters
      if (filters.created_from || filters.created_to) {
        where.created_at = {};
        if (filters.created_from)
          where.created_at.gte = new Date(filters.created_from);
        if (filters.created_to)
          where.created_at.lte = new Date(filters.created_to);
      }

      // Search functionality
      if (search) {
        where.OR = [
          { application_number: { contains: search, mode: 'insensitive' } },
          { user: { name: { contains: search, mode: 'insensitive' } } },
          { user: { email: { contains: search, mode: 'insensitive' } } },
          { guest_name: { contains: search, mode: 'insensitive' } },
          { guest_email: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [applications, total] = await Promise.all([
        this.prisma.application.findMany({
          where,
          include: {
            user: {
              select: { id: true, name: true, email: true, mobileNo: true },
            },
            workflow_template: {
              select: {
                id: true,
                name: true,
                description: true,
                workflowTemplate: true,
              },
            },
          },
          orderBy: { created_at: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.application.count({ where }),
      ]);

      // Enhance applications with agent details and service names
      const enhancedApplications =
        await this.enhanceApplicationsWithDetails(applications);

      // Transform applications using transformer service
      return this.transformerService.createPaginatedResponse(
        enhancedApplications,
        total,
        page,
        limit,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get applications with transformation: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Enhance applications with agent details and service names
   */
  private async enhanceApplicationsWithDetails(
    applications: any[],
  ): Promise<any[]> {
    try {
      // Collect all unique agent IDs from all applications
      const allAgentIds = new Set<string>();
      applications.forEach((app) => {
        if (app.agent_ids && Array.isArray(app.agent_ids)) {
          app.agent_ids.forEach((id: string) => allAgentIds.add(id));
        }
      });

      // Fetch all agents in a single query
      const agents = await this.prisma.agent.findMany({
        where: { id: { in: Array.from(allAgentIds) } },
        select: { id: true, name: true, email: true },
      });

      // Create a map for quick agent lookup
      const agentMap = new Map(agents.map((agent) => [agent.id, agent]));

      // Enhance each application with agent details and service names
      const enhancedApplications = await Promise.all(
        applications.map(async (app) => {
          // Resolve agent details
          const agentDetails =
            app.agent_ids && Array.isArray(app.agent_ids)
              ? app.agent_ids
                  .map((agentId: string) => agentMap.get(agentId))
                  .filter(Boolean)
              : [];

          // Resolve service name
          const serviceResult = await this.resolveServiceName(
            app.service_type,
            app.service_id,
          );

          const serviceName =
            typeof serviceResult === 'string'
              ? serviceResult
              : serviceResult.serviceName;
          const serviceId =
            typeof serviceResult === 'string'
              ? app.service_id
              : serviceResult.serviceId;

          return {
            ...app,
            agent_details: agentDetails,
            service_name: serviceName,
            service_id: serviceId,
            workflow_template_name: app.workflow_template?.name || null,
          };
        }),
      );

      return enhancedApplications;
    } catch (error) {
      this.logger.error(
        `Failed to enhance applications with details: ${error.message}`,
        error.stack,
      );
      // Return original applications if enhancement fails
      return applications;
    }
  }

  /**
   * Update document status with email notification
   */
  async updateDocumentStatus(
    documentId: string,
    updateDto: UpdateDocumentStatusDto,
    reviewedBy: string,
  ): Promise<any> {
    try {
      this.logger.log(
        `Updating document status: ${documentId} to ${updateDto.status}`,
      );

      // Find the application document
      const applicationDocument =
        await this.prisma.application_document.findUnique({
          where: { id: documentId },
          include: {
            application: {
              include: {
                user: {
                  select: { id: true, name: true, email: true },
                },
              },
            },
            document: {
              select: {
                id: true,
                document_name: true,
                original_filename: true,
              },
            },
          },
        });

      if (!applicationDocument) {
        throw new NotFoundException(
          `Application document not found: ${documentId}`,
        );
      }

      // Validate that reason is provided for statuses that require it
      const statusesRequiringReason = [
        DocumentStatusEnum.REJECTED,
        DocumentStatusEnum.REQUIRED_REVISION,
        DocumentStatusEnum.EXPIRED,
        DocumentStatusEnum.UNDER_REVIEW,
      ];

      if (
        statusesRequiringReason.includes(updateDto.status) &&
        !updateDto.reason
      ) {
        throw new BadRequestException(
          `Reason is required for status: ${updateDto.status}`,
        );
      }

      // Update the document status
      const updatedDocument = await this.prisma.application_document.update({
        where: { id: documentId },
        data: {
          status: updateDto.status.toLowerCase(), // Convert enum to lowercase for database
          reviewed_by: reviewedBy,
          reviewed_at: new Date(),
          review_comments: updateDto.reviewComments,
          rejection_reason: updateDto.reason,
        },
      });

      // Send email notification to application owner
      await this.sendDocumentStatusNotification(
        applicationDocument,
        updateDto.status,
        updateDto.reason,
        updateDto.reviewComments,
      );

      this.logger.log(`Document status updated successfully: ${documentId}`);

      return {
        id: updatedDocument.id,
        status: updateDto.status,
        reviewedBy: reviewedBy,
        reviewedAt: updatedDocument.reviewed_at,
        reviewComments: updatedDocument.review_comments,
        rejectionReason: updatedDocument.rejection_reason,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update document status: ${documentId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Request a new document from applicant
   */
  async requestDocument(
    applicationId: string,
    requestDto: RequestDocumentDto,
    requestedBy: string,
  ): Promise<any> {
    try {
      this.logger.log(`Requesting document for application: ${applicationId}`);

      // Check if application exists
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
        },
      });

      if (!application) {
        throw new NotFoundException(`Application not found: ${applicationId}`);
      }

      // Check for duplicate document name in this application
      const existingDocument = await this.prisma.application_document.findFirst(
        {
          where: {
            application_id: applicationId,
            file_name: {
              equals: requestDto.documentName,
              mode: 'insensitive',
            },
          },
        },
      );

      if (existingDocument) {
        throw new BadRequestException(
          `A document with name "${requestDto.documentName}" already exists for this application`,
        );
      }

      // Create a placeholder document in document_vault
      const placeholderDocument = await this.prisma.document_vault.create({
        data: {
          document_name: requestDto.documentName,
          original_filename: requestDto.documentName,
          document_type: 'Other',
          document_category: requestDto.documentCategory || 'requested',
          file_path: '', // Empty until actual file is uploaded
          file_size: 0,
          user_id: application.user_id,
          guest_email: application.guest_email,
          uploaded_by: requestedBy,
        },
      });

      // Create the application document request
      const documentRequest = await this.prisma.application_document.create({
        data: {
          application_id: applicationId,
          document_vault_id: placeholderDocument.id,
          stage_order: requestDto.stageOrder || 1,
          file_name: requestDto.documentName,
          file_url: '', // Empty until actual file is uploaded
          required: requestDto.required || false,
          status: 'request', // Set status to 'request'
          request_reason: requestDto.reason,
          requested_by: requestedBy,
          upload_date: new Date(),
        },
      });

      // Send email notification to application owner
      await this.sendDocumentRequestNotification(
        application,
        requestDto.documentName,
        requestDto.reason,
      );

      this.logger.log(
        `Document request created successfully: ${documentRequest.id}`,
      );

      return {
        id: documentRequest.id,
        applicationId: applicationId,
        documentName: requestDto.documentName,
        status: 'request',
        requestReason: requestDto.reason,
        requestedBy: requestedBy,
        createdAt: documentRequest.created_at,
      };
    } catch (error) {
      this.logger.error(
        `Failed to request document for application: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Send email notification for document status update using new email templates
   */
  private async sendDocumentStatusNotification(
    applicationDocument: any,
    status: DocumentStatusEnum,
    reason?: string,
    reviewComments?: string,
  ): Promise<void> {
    try {
      const recipientEmail =
        applicationDocument.application.user?.email ||
        applicationDocument.application.guest_email;
      const recipientName =
        applicationDocument.application.user?.name ||
        applicationDocument.application.guest_name;

      if (!recipientEmail || !recipientName) {
        this.logger.warn(
          'No email address or name found for document status notification',
        );
        return;
      }

      const documentName = applicationDocument.document.document_name;
      const applicationNumber =
        applicationDocument.application.application_number;

      // For rejected documents, use the document rejection email template
      if (
        status === DocumentStatusEnum.REJECTED ||
        status === DocumentStatusEnum.REQUIRED_REVISION
      ) {
        await this.sendDocumentRejectionEmailTemplate(
          applicationDocument,
          status,
          reason,
          reviewComments,
        );
        return;
      }

      // For other statuses, use the existing notification service
      let subject = '';
      let message = '';

      switch (status) {
        case DocumentStatusEnum.APPROVED:
          subject = `Document Approved - ${documentName}`;
          message = `Your document "${documentName}" for application ${applicationNumber} has been approved.`;
          break;
        case DocumentStatusEnum.EXPIRED:
          subject = `Document Expired - ${documentName}`;
          message = `Your document "${documentName}" for application ${applicationNumber} has expired. Reason: ${reason}`;
          break;
        case DocumentStatusEnum.UNDER_REVIEW:
          subject = `Document Under Review - ${documentName}`;
          message = `Your document "${documentName}" for application ${applicationNumber} is now under review. ${reason ? `Note: ${reason}` : ''}`;
          break;
        default:
          subject = `Document Status Updated - ${documentName}`;
          message = `Your document "${documentName}" for application ${applicationNumber} status has been updated to ${status}.`;
      }

      if (reviewComments) {
        message += `\n\nAdditional Comments: ${reviewComments}`;
      }

      await this.notificationService.sendNotification({
        recipient_email: recipientEmail,
        subject,
        message_body: message,
        notification_type: 'Email',
        application_id: applicationDocument.application_id,
      });

      this.logger.log(
        `Document status notification sent to: ${recipientEmail}`,
      );
    } catch (error) {
      this.logger.error('Failed to send document status notification', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Send document rejection email using the new email template
   */
  private async sendDocumentRejectionEmailTemplate(
    applicationDocument: any,
    status: DocumentStatusEnum,
    reason?: string,
    reviewComments?: string,
  ): Promise<void> {
    try {
      const recipientEmail =
        applicationDocument.application.user?.email ||
        applicationDocument.application.guest_email;
      const recipientName =
        applicationDocument.application.user?.name ||
        applicationDocument.application.guest_name;

      if (!recipientEmail || !recipientName) {
        return;
      }

      const documentName = applicationDocument.document.document_name;
      const applicationNumber =
        applicationDocument.application.application_number;

      // Get service name from workflow template if available
      let serviceName = 'Career Ireland Service';
      if (applicationDocument.application.workflow_template_id) {
        const workflowTemplate = await this.prisma.workflow_template.findUnique(
          {
            where: { id: applicationDocument.application.workflow_template_id },
            select: { name: true },
          },
        );
        if (workflowTemplate) {
          serviceName = workflowTemplate.name;
        }
      }

      // Prepare specific issues array
      const specificIssues: string[] = [];
      if (reviewComments) {
        specificIssues.push(reviewComments);
      }

      // Prepare rejected documents array
      const rejectedDocuments = [
        {
          documentName: documentName,
          rejectionReason: reason || 'Document requires attention',
          specificIssues:
            specificIssues.length > 0 ? specificIssues : undefined,
        },
      ];

      // Prepare email data
      const emailData = {
        applicantName: recipientName,
        applicationId: applicationNumber,
        serviceName: serviceName,
        rejectedDocuments: rejectedDocuments,
        rejectionDate: new Date(),
        resubmissionDeadline: this.calculateResubmissionDeadline(),
        websiteUrl: `${process.env.WEBSITE}/auth/login`,
        supportEmail: process.env.EMAIL || '<EMAIL>',
        generalGuidelines: [
          'Ensure documents are clear and legible',
          'Use high-quality scans or photos',
          'Save files in PDF, JPG, or PNG format',
          'Make sure all text is readable',
          'Include all required information',
        ],
      };

      // Send the email using the new template
      await this.emailTemplateService.sendDocumentRejectionEmail(
        recipientEmail,
        emailData,
      );

      this.logger.log(
        `Document rejection email sent successfully to: ${recipientEmail}`,
      );
    } catch (error) {
      this.loggerService.error(
        'Failed to send document rejection email',
        error,
        {
          applicationId: applicationDocument.application_id,
          documentId: applicationDocument.id,
          service: 'ApplicationService',
        },
      );
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Calculate resubmission deadline (7 days from now)
   */
  private calculateResubmissionDeadline(): string {
    const deadline = new Date();
    deadline.setDate(deadline.getDate() + 7);
    return deadline.toLocaleDateString('en-IE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  /**
   * Send email notification for document request using new email template
   */
  private async sendDocumentRequestNotification(
    application: any,
    documentName: string,
    reason: string,
  ): Promise<void> {
    try {
      const recipientEmail = application.user?.email || application.guest_email;
      const recipientName = application.user?.name || application.guest_name;

      if (!recipientEmail || !recipientName) {
        this.logger.warn(
          'No email address or name found for document request notification',
        );
        return;
      }

      // Get service name from workflow template if available
      let serviceName = 'Career Ireland Service';
      if (application.workflow_template_id) {
        const workflowTemplate = await this.prisma.workflow_template.findUnique(
          {
            where: { id: application.workflow_template_id },
            select: { name: true },
          },
        );
        if (workflowTemplate) {
          serviceName = workflowTemplate.name;
        }
      }

      // Calculate deadline (7 days from now)
      const deadline = this.calculateResubmissionDeadline();

      // Prepare email data
      const emailData = {
        recipientName: recipientName,
        requesterName: 'Careerireland Team',
        applicationId: application.application_number,
        serviceName: serviceName,
        documentsNeeded: [documentName],
        deadline: deadline,
        additionalInstructions: reason,
      };

      // Send the email using the new template
      await this.emailTemplateService.sendDocumentRequestEmail(
        recipientEmail,
        emailData,
      );

      this.logger.log(
        `Document request notification sent to: ${recipientEmail}`,
      );
    } catch (error) {
      this.loggerService.error(
        'Failed to send document request notification',
        error,
        {
          applicationId: application.id,
          documentName,
          service: 'ApplicationService',
        },
      );
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Update application priority (admin-only)
   */
  async updateApplicationPriority(
    applicationId: string,
    priorityLevel: 'Low' | 'Medium' | 'High' | 'Critical',
    updatedBy: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Updating application priority: ${applicationId} to ${priorityLevel} by ${updatedBy}`,
      );

      // Update the application priority
      await this.prisma.application.update({
        where: { id: applicationId },
        data: {
          priority_level: priorityLevel as any, // Cast to match Prisma enum
        },
      });

      this.logger.log(
        `Application priority updated successfully: ${applicationId} to ${priorityLevel}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update application priority: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Assign application to agent
   */
  async assignApplicationToAgent(
    applicationId: string,
    agentId: string,
    assignedBy: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Assigning application: ${applicationId} to agent: ${agentId} by ${assignedBy}`,
      );

      // Verify agent exists and is active
      const agent = await this.prisma.agent.findUnique({
        where: { id: agentId },
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      if (agent.status !== 'Active') {
        throw new BadRequestException('Cannot assign to inactive agent');
      }

      // Get current application to check existing agent assignments
      const currentApplication = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: { agent_ids: true },
      });

      if (!currentApplication) {
        throw new NotFoundException('Application not found');
      }

      // Add agent to the array if not already assigned
      const currentAgentIds = currentApplication.agent_ids || [];
      if (!currentAgentIds.includes(agentId)) {
        const updatedAgentIds = [...currentAgentIds, agentId];

        await this.prisma.application.update({
          where: { id: applicationId },
          data: {
            agent_ids: updatedAgentIds,
          },
        });
      }

      this.logger.log(
        `Application assigned successfully: ${applicationId} to agent: ${agentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to assign application: ${applicationId} to agent: ${agentId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove agent from application
   * Removes specified agent ID from the agent_ids array
   */
  async removeAgentFromApplication(
    applicationId: string,
    agentId: string,
    removedBy: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Removing agent: ${agentId} from application: ${applicationId} by ${removedBy}`,
      );

      // Verify application exists
      const currentApplication = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: { id: true, agent_ids: true },
      });

      if (!currentApplication) {
        throw new NotFoundException('Application not found');
      }

      // Verify agent exists (optional validation)
      const agent = await this.prisma.agent.findUnique({
        where: { id: agentId },
        select: { id: true },
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Remove agent from the array if present
      const currentAgentIds = currentApplication.agent_ids || [];
      const updatedAgentIds = currentAgentIds.filter((id) => id !== agentId);

      // Update the application even if agent wasn't in the array (idempotent operation)
      await this.prisma.application.update({
        where: { id: applicationId },
        data: {
          agent_ids: updatedAgentIds,
        },
      });

      this.logger.log(
        `Agent removed successfully: ${agentId} from application: ${applicationId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to remove agent: ${agentId} from application: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get application prefix based on service type
   */
  private getApplicationPrefix(serviceType: string): string {
    const prefixMapping: Record<string, string> = {
      immigration: 'IMM',
      service: 'SRV',
      package: 'PKG',
      training: 'TRN',
    };

    return prefixMapping[serviceType] || 'APP';
  }

  /**
   * Resolve service name based on service type and service ID
   */
  async resolveServiceName(
    serviceType: string,
    serviceId?: string,
  ): Promise<{ serviceName: string; serviceId: string } | string> {
    try {
      if (!serviceId) {
        return `${serviceType} service`;
      }

      let service: any = null;

      // Query the appropriate table based on service type
      switch (serviceType) {
        case 'immigration':
          service = await this.prisma.immigration_service.findUnique({
            where: { id: serviceId },
            select: { name: true },
          });
          break;
        case 'service':
          service = await this.prisma.service.findUnique({
            where: { id: serviceId },
            select: { name: true },
          });
          break;
        case 'package':
          service = await this.prisma.packages.findUnique({
            where: { id: serviceId },
            select: { name: true },
          });
          break;
        case 'training':
          service = await this.prisma.training.findUnique({
            where: { id: serviceId },
            select: { name: true },
          });
          break;
        default:
          this.logger.warn(`Unknown service type: ${serviceType}`);
          return `${serviceType} service`;
      }

      if (!service) {
        this.logger.warn(`Service not found: ${serviceType}/${serviceId}`);
        return `${serviceType} service`;
      }

      return { serviceName: service.name, serviceId };
    } catch (error) {
      this.logger.error(
        `Failed to resolve service name for ${serviceType}/${serviceId}: ${error.message}`,
        error.stack,
      );
      return `${serviceType} service`;
    }
  }

  /**
   * Assign a new workflow template to an application with atomic cleanup
   */
  async assignWorkflowTemplate(
    dto: AssignWorkflowTemplateDto,
  ): Promise<AssignWorkflowTemplateResponseDto> {
    try {
      this.logger.log(
        `Assigning workflow template ${dto.new_workflow_template_id} to application ${dto.application_id}`,
      );

      // Validate that application exists
      const application = await this.prisma.application.findUnique({
        where: { id: dto.application_id },
        select: {
          id: true,
          workflow_template_id: true,
          service_type: true,
          service_id: true,
        },
      });

      if (!application) {
        throw new NotFoundException(
          `Application with ID ${dto.application_id} not found`,
        );
      }

      // Validate that new workflow template exists
      const newWorkflowTemplate =
        await this.prisma.workflow_template.findUnique({
          where: { id: dto.new_workflow_template_id },
          select: {
            id: true,
            name: true,
            isActive: true,
            serviceType: true,
            serviceId: true,
            workflowTemplate: true,
          },
        });

      if (!newWorkflowTemplate) {
        throw new NotFoundException(
          `Workflow template with ID ${dto.new_workflow_template_id} not found`,
        );
      }

      if (!newWorkflowTemplate.isActive) {
        throw new BadRequestException(
          `Workflow template ${dto.new_workflow_template_id} is not active`,
        );
      }

      // Check if the new workflow template is compatible with the application's service
      if (newWorkflowTemplate.serviceType !== application.service_type) {
        throw new BadRequestException(
          `Workflow template service type (${newWorkflowTemplate.serviceType}) does not match application service type (${application.service_type})`,
        );
      }

      // If workflow template has a specific serviceId, validate it matches
      if (
        newWorkflowTemplate.serviceId &&
        newWorkflowTemplate.serviceId !== application.service_id
      ) {
        throw new BadRequestException(
          `Workflow template is specific to service ID ${newWorkflowTemplate.serviceId} but application uses service ID ${application.service_id}`,
        );
      }

      // Check if application already has this workflow template
      if (application.workflow_template_id === dto.new_workflow_template_id) {
        throw new BadRequestException(
          `Application already uses workflow template ${dto.new_workflow_template_id}`,
        );
      }

      // Validate workflow template structure
      const workflowStages = newWorkflowTemplate.workflowTemplate as any[];
      if (!workflowStages || !Array.isArray(workflowStages)) {
        throw new BadRequestException(
          `Invalid workflow template structure: template must contain an array of stages`,
        );
      }

      if (workflowStages.length === 0) {
        throw new BadRequestException(
          `Invalid workflow template: template must contain at least one stage`,
        );
      }

      let formRecordsCleaned = 0;
      let documentRecordsCleaned = 0;
      let storageFilesCleaned = 0;
      let filePaths: string[] = [];

      // Perform atomic transaction
      await this.prisma.$transaction(async (tx) => {
        // Get existing form and document records for cleanup tracking
        const [existingFormRecords, existingDocumentRecords] =
          await Promise.all([
            tx.application_form.findMany({
              where: { application_id: dto.application_id },
              select: { id: true },
            }),
            tx.application_document.findMany({
              where: { application_id: dto.application_id },
              include: {
                document: {
                  select: { file_path: true },
                },
              },
            }),
          ]);

        formRecordsCleaned = existingFormRecords.length;
        documentRecordsCleaned = existingDocumentRecords.length;

        // Collect file paths for storage cleanup
        filePaths = existingDocumentRecords
          .map((doc) => doc.document?.file_path)
          .filter(Boolean) as string[];

        // Delete existing application_form records
        if (formRecordsCleaned > 0) {
          await tx.application_form.deleteMany({
            where: { application_id: dto.application_id },
          });
          this.logger.debug(
            `Deleted ${formRecordsCleaned} form records for application ${dto.application_id}`,
          );
        }

        // Delete existing application_document records
        if (documentRecordsCleaned > 0) {
          await tx.application_document.deleteMany({
            where: { application_id: dto.application_id },
          });
          this.logger.debug(
            `Deleted ${documentRecordsCleaned} document records for application ${dto.application_id}`,
          );
        }

        // Update application with new workflow template
        await tx.application.update({
          where: { id: dto.application_id },
          data: {
            workflow_template_id: dto.new_workflow_template_id,
            current_step: '1', // Reset to first step
            updated_at: new Date(),
          },
        });

        // Create new form records based on new workflow template
        const workflowStages = newWorkflowTemplate.workflowTemplate as any[];
        if (workflowStages && Array.isArray(workflowStages)) {
          for (const stage of workflowStages) {
            if (
              stage.customFormRequired &&
              stage.customForm &&
              Array.isArray(stage.customForm)
            ) {
              const formRecords = stage.customForm.map((field: any) => ({
                application_id: dto.application_id,
                stage_order: stage.stageOrder || 1,
                field_name: field.fieldName || field.name || 'unnamed_field',
                field_type: field.fieldType || field.type || 'text',
                required: Boolean(field.required),
                field_value: null,
                field_options: field.options
                  ? JSON.stringify(field.options)
                  : null,
                show_to_client: Boolean(field.showToClient !== false), // Default to true
              }));

              if (formRecords.length > 0) {
                await tx.application_form.createMany({
                  data: formRecords,
                });
              }
            }
          }
        }

        // Create new document records based on new workflow template
        for (const stage of workflowStages) {
          if (
            stage.documentsRequired &&
            stage.documents &&
            Array.isArray(stage.documents)
          ) {
            // Create document records one by one to handle document_vault creation
            for (const doc of stage.documents) {
              try {
                // Create a temporary document_vault record first to satisfy unique constraint
                const tempDocument = await tx.document_vault.create({
                  data: {
                    document_name:
                      doc.documentName || doc.name || 'unnamed_document',
                    original_filename:
                      doc.documentName || doc.name || 'unnamed_document',
                    document_type: 'Other', // Default type for template placeholders
                    document_category: 'required',
                    file_path: '', // Empty until actual file is uploaded
                    file_size: 0,
                    user_id: null, // Will be set when document is uploaded
                    guest_email: null,
                    uploaded_by: null,
                  },
                });

                // Now create the application_document record with the proper document_vault_id
                await tx.application_document.create({
                  data: {
                    application_id: dto.application_id,
                    document_vault_id: tempDocument.id,
                    stage_order: stage.stageOrder || 1,
                    file_name:
                      doc.documentName || doc.name || 'unnamed_document',
                    file_url: '', // Will be populated when documents are uploaded
                    required: Boolean(doc.required),
                    status: 'pending',
                    request_reason: doc.description || null,
                  },
                });

                this.logger.debug(
                  `Created document requirement: ${doc.documentName || doc.name} for application: ${dto.application_id}`,
                );
              } catch (recordError) {
                this.logger.error(
                  `Failed to create document requirement: ${doc.documentName || doc.name} for application: ${dto.application_id}`,
                  recordError.stack,
                );

                // If this is a critical error (like constraint violations), throw it
                if (
                  recordError.code === 'P2002' ||
                  recordError.message?.includes('Unique constraint')
                ) {
                  throw new BadRequestException(
                    `Failed to create document requirement due to duplicate entry. Please try again.`,
                  );
                }

                // For other errors, continue with other documents but log the issue
                this.logger.warn(
                  `Skipping document requirement: ${doc.documentName || doc.name} due to error: ${recordError.message}`,
                );
              }
            }
          }
        }

        // Clean up storage files (outside transaction to avoid blocking)
        if (filePaths.length > 0) {
          // Note: Storage cleanup is done after transaction to avoid blocking
          // We'll handle this after the transaction completes
          this.logger.debug(
            `Scheduled ${filePaths.length} files for storage cleanup`,
          );
        }
      });

      // Clean up storage files after successful transaction
      if (filePaths.length > 0) {
        try {
          const cleanupResult =
            await this.mediaService.deleteMultipleFiles(filePaths);
          storageFilesCleaned = cleanupResult.success.length;

          if (cleanupResult.failed.length > 0) {
            this.logger.warn(
              `Failed to clean up ${cleanupResult.failed.length} storage files for application ${dto.application_id}`,
              { failedFiles: cleanupResult.failed },
            );
          }
        } catch (storageError) {
          this.logger.error(
            `Failed to clean up storage files for application ${dto.application_id}: ${storageError.message}`,
            storageError.stack,
          );
          // Don't fail the entire operation for storage cleanup issues
        }
      }

      this.logger.log(
        `Successfully assigned workflow template ${dto.new_workflow_template_id} to application ${dto.application_id}. ` +
          `Cleaned up: ${formRecordsCleaned} forms, ${documentRecordsCleaned} documents, ${storageFilesCleaned} files`,
      );

      // Send workflow template assignment email
      await this.sendWorkflowTemplateAssignmentEmail(
        dto.application_id,
        dto.new_workflow_template_id,
      );

      return {
        success: true,
        message: 'Workflow template assigned successfully',
        application_id: dto.application_id,
        workflow_template_id: dto.new_workflow_template_id,
        form_records_cleaned: formRecordsCleaned,
        document_records_cleaned: documentRecordsCleaned,
        storage_files_cleaned: storageFilesCleaned,
      };
    } catch (error) {
      this.logger.error(
        `Failed to assign workflow template: ${error.message}`,
        error.stack,
      );

      // Re-throw known exceptions
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      // Handle specific database errors
      if (error.code === 'P2002') {
        throw new BadRequestException(
          'Failed to assign workflow template due to duplicate data. Please ensure the application is not being processed simultaneously.',
        );
      }

      if (error.code === 'P2025') {
        throw new NotFoundException(
          'One or more required records were not found during workflow template assignment.',
        );
      }

      // Wrap unknown errors with user-friendly message
      throw new BadRequestException(
        `Failed to assign workflow template. Please try again or contact support if the issue persists.`,
      );
    }
  }

  /**
   * Update application status with conditional email notifications
   */
  async updateApplicationStatus(
    applicationId: string,
    updateDto: UpdateApplicationStatusDto,
    updatedBy: string,
  ): Promise<UpdateApplicationStatusResponseDto> {
    try {
      this.logger.log(
        `Updating application status: ${applicationId} to ${updateDto.status} by ${updatedBy}`,
      );

      // Log the status update attempt
      this.loggerService.info('Application status update initiated', {
        applicationId,
        newStatus: updateDto.status,
        updatedBy,
        service: 'ApplicationService',
      });

      // Find the application with user information
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
        },
      });

      if (!application) {
        this.loggerService.error(
          'Application not found for status update',
          new Error(`Application not found: ${applicationId}`),
          { applicationId, updatedBy, service: 'ApplicationService' },
        );
        throw new NotFoundException(`Application not found: ${applicationId}`);
      }

      const previousStatus = application.status;

      // Validate status transition (optional business logic)
      if (previousStatus === updateDto.status) {
        this.loggerService.warn(
          'Attempted to update application to same status',
          {
            applicationId,
            currentStatus: previousStatus,
            requestedStatus: updateDto.status,
            updatedBy,
            service: 'ApplicationService',
          },
        );
        throw new BadRequestException(
          `Application is already in ${updateDto.status} status`,
        );
      }

      // Update the application status
      const updatedApplication = await this.prisma.application.update({
        where: { id: applicationId },
        data: {
          status: updateDto.status as any, // Cast to match Prisma enum
        },
      });

      this.loggerService.info('Application status updated successfully', {
        applicationId,
        previousStatus,
        newStatus: updateDto.status,
        updatedBy,
        service: 'ApplicationService',
      });

      // Check if email notification should be sent
      let emailNotificationSent = false;

      if (application.user_id) {
        try {
          const shouldSendNotification =
            await this.shouldSendStatusUpdateNotification(application.user_id);

          if (shouldSendNotification) {
            try {
              await this.sendStatusUpdateNotification(
                application,
                previousStatus,
                updateDto.status,
              );
              emailNotificationSent = true;

              this.loggerService.info('Status update email notification sent', {
                applicationId,
                recipientEmail:
                  application.user?.email || application.guest_email,
                newStatus: updateDto.status,
                service: 'ApplicationService',
              });
            } catch (notificationError) {
              this.loggerService.error(
                'Failed to send status update notification',
                notificationError,
                {
                  applicationId,
                  newStatus: updateDto.status,
                  recipientEmail:
                    application.user?.email || application.guest_email,
                  service: 'ApplicationService',
                },
              );
              // Don't fail the status update if notification fails
              emailNotificationSent = false;
            }
          } else {
            this.loggerService.info(
              'Status update email notification skipped - user preference disabled',
              {
                applicationId,
                userId: application.user_id,
                newStatus: updateDto.status,
                service: 'ApplicationService',
              },
            );
          }
        } catch (notificationError) {
          this.loggerService.error(
            'Failed to check notification preference for status update',
            notificationError,
            {
              applicationId,
              userId: application.user_id,
              service: 'ApplicationService',
            },
          );
          // Don't fail the status update if notification check fails
        }
      } else {
        this.loggerService.info(
          'Status update notification skipped - no user associated with application',
          {
            applicationId,
            newStatus: updateDto.status,
            service: 'ApplicationService',
          },
        );
      }

      this.logger.log(
        `Application status updated successfully: ${applicationId} from ${previousStatus} to ${updateDto.status}`,
      );

      return {
        success: true,
        message: 'Application status updated successfully',
        data: {
          applicationId: updatedApplication.id,
          previousStatus,
          newStatus: updateDto.status,
          updatedAt: updatedApplication.updated_at.toISOString(),
          emailNotificationSent,
        },
      };
    } catch (error) {
      this.loggerService.error('Failed to update application status', error, {
        applicationId,
        requestedStatus: updateDto.status,
        updatedBy,
        service: 'ApplicationService',
      });

      this.logger.error(
        `Failed to update application status: ${applicationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Check if user should receive status update notifications
   */
  private async shouldSendStatusUpdateNotification(
    userId: string,
  ): Promise<boolean> {
    try {
      return await this.notificationService.shouldReceiveNotification(
        userId,
        'case_status_update',
      );
    } catch (error) {
      this.loggerService.error(
        'Failed to check notification preference for status update',
        error,
        { userId, service: 'ApplicationService' },
      );
      // Default to sending notification if there's an error checking preferences
      return true;
    }
  }

  /**
   * Send status update notification email using new email template
   */
  private async sendStatusUpdateNotification(
    application: any,
    previousStatus: string,
    newStatus: string,
  ): Promise<void> {
    try {
      const recipientEmail = application.user?.email || application.guest_email;
      const recipientName = application.user?.name || application.guest_name;

      if (!recipientEmail || !recipientName) {
        this.loggerService.warn(
          'No email address or name found for status update notification',
          {
            applicationId: application.id,
            applicationNumber: application.application_number,
            service: 'ApplicationService',
          },
        );
        return;
      }

      // Get workflow template information for service name
      let serviceName = 'Career Ireland Service';
      let nextSteps: string[] = [];

      if (application.workflow_template_id) {
        const workflowTemplate = await this.prisma.workflow_template.findUnique(
          {
            where: { id: application.workflow_template_id },
            select: {
              name: true,
              description: true,
            },
          },
        );

        if (workflowTemplate) {
          serviceName = workflowTemplate.name;
        }
      }

      // Generate next steps based on status
      nextSteps = this.generateNextStepsForStatus(newStatus);

      // Prepare email data
      const emailData = {
        applicantName: recipientName,
        applicationId: application.application_number,
        serviceName: serviceName,
        previousStatus: previousStatus,
        currentStatus: newStatus,
        statusChangeDate: new Date(),
        nextSteps: nextSteps,
        additionalNotes: this.getStatusChangeMessage(previousStatus, newStatus),
        websiteUrl: `${process.env.WEBSITE}/auth/login`,
      };

      // Send the email using the new template
      await this.emailTemplateService.sendApplicationStatusChangeEmail(
        recipientEmail,
        emailData,
      );

      this.logger.log(`Status update notification sent to: ${recipientEmail}`);
    } catch (error) {
      this.loggerService.error(
        'Failed to send status update notification',
        error,
        {
          applicationId: application.id,
          newStatus,
          recipientEmail: application.user?.email || application.guest_email,
          service: 'ApplicationService',
        },
      );
      // Re-throw error so it can be handled in the main flow
      throw error;
    }
  }

  /**
   * Generate next steps based on application status
   */
  private generateNextStepsForStatus(status: string): string[] {
    switch (status.toLowerCase()) {
      case 'pending':
        return [
          'Upload any required documents',
          'Complete any outstanding forms',
          'Wait for review by our team',
        ];
      case 'in_review':
      case 'under_review':
        return [
          'Your application is being reviewed',
          'You will be contacted if additional information is needed',
          'Check your email regularly for updates',
        ];
      case 'approved':
        return [
          'Congratulations! Your application has been approved',
          'Check your email for next steps',
          'Contact us if you have any questions',
        ];
      case 'rejected':
      case 'declined':
        return [
          'Review the rejection reasons provided',
          'Contact our support team for clarification',
          'Consider reapplying with updated information',
        ];
      case 'completed':
        return [
          'Your application process is complete',
          'Keep this confirmation for your records',
          'Contact us if you need any additional assistance',
        ];
      case 'documents_required':
      case 'awaiting_documents':
        return [
          'Upload the required documents as soon as possible',
          'Ensure all documents are clear and legible',
          'Contact us if you need help with document requirements',
        ];
      default:
        return [
          'Log in to your account to view application details',
          'Contact us if you have any questions',
        ];
    }
  }

  /**
   * Get status change message based on status transition
   */
  private getStatusChangeMessage(
    previousStatus: string,
    newStatus: string,
  ): string {
    switch (newStatus.toLowerCase()) {
      case 'approved':
        return 'Congratulations! Your application has been approved. We will be in touch with the next steps shortly.';
      case 'rejected':
      case 'declined':
        return 'We regret to inform you that your application has been declined. Please contact our support team for more information.';
      case 'completed':
        return 'Your application has been successfully completed. Thank you for choosing our services.';
      case 'in_review':
      case 'under_review':
        return 'Your application is now being reviewed by our team. We will update you on the progress soon.';
      case 'documents_required':
      case 'awaiting_documents':
        return 'Additional documents are required to proceed with your application. Please upload them at your earliest convenience.';
      default:
        return `Your application status has been updated to ${newStatus}. Please log in to your account for more details.`;
    }
  }

  /**
   * Send application creation email to application user
   */
  private async sendApplicationCreationEmail(
    applicationId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Sending application creation email for application: ${applicationId}`,
      );

      // Get application details with user/guest information and workflow template
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: {
          id: true,
          application_number: true,
          service_type: true,
          service_id: true,
          user_id: true,
          guest_name: true,
          guest_email: true,
          created_at: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          workflow_template: {
            select: {
              id: true,
              name: true,
              description: true,
              workflowTemplate: true,
            },
          },
        },
      });

      if (!application) {
        this.logger.warn(
          `Application not found for email sending: ${applicationId}`,
        );
        return;
      }

      // Determine recipient email and name
      const recipientEmail = application.user?.email || application.guest_email;
      const recipientName = application.user?.name || application.guest_name;

      if (!recipientEmail || !recipientName) {
        this.logger.warn(
          `No recipient email or name found for application: ${applicationId}`,
        );
        return;
      }

      // Extract required documents from workflow template if available
      let requiredDocuments: string[] = [];
      let serviceName = 'Career Ireland Service';

      if (application.workflow_template) {
        requiredDocuments = this.extractRequiredDocumentsFromTemplate(
          application.workflow_template.workflowTemplate as any[],
        );
        serviceName = application.workflow_template.name;
      }

      // Prepare email data
      const emailData = {
        applicantName: recipientName,
        applicationId: application.application_number,
        serviceName: serviceName,
        requiredDocuments:
          requiredDocuments.length > 0
            ? requiredDocuments
            : [
                'Required documents will be specified based on your application',
              ],
        websiteUrl: `${process.env.WEBSITE}/auth/login`,
        applicationCreatedDate: application.created_at,
        isAutoGenerated: false, // This is a manual application creation
      };

      // Send the email
      await this.emailTemplateService.sendApplicationRequirementsEmail(
        recipientEmail,
        emailData,
      );

      this.logger.log(
        `Application creation email sent successfully to: ${recipientEmail}`,
      );
    } catch (error) {
      this.loggerService.error(
        'Failed to send application creation email',
        error,
        {
          applicationId,
          service: 'ApplicationService',
        },
      );
      // Don't throw error to avoid failing the main application creation operation
    }
  }

  /**
   * Send workflow template assignment email to application user
   */
  private async sendWorkflowTemplateAssignmentEmail(
    applicationId: string,
    workflowTemplateId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Sending workflow template assignment email for application: ${applicationId}`,
      );

      // Get application details with user/guest information
      const application = await this.prisma.application.findUnique({
        where: { id: applicationId },
        select: {
          id: true,
          application_number: true,
          service_type: true,
          service_id: true,
          user_id: true,
          guest_name: true,
          guest_email: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!application) {
        this.logger.warn(
          `Application not found for email sending: ${applicationId}`,
        );
        return;
      }

      // Get workflow template details
      const workflowTemplate = await this.prisma.workflow_template.findUnique({
        where: { id: workflowTemplateId },
        select: {
          id: true,
          name: true,
          description: true,
          workflowTemplate: true,
        },
      });

      if (!workflowTemplate) {
        this.logger.warn(
          `Workflow template not found for email sending: ${workflowTemplateId}`,
        );
        return;
      }

      // Extract required documents from workflow template
      const requiredDocuments = this.extractRequiredDocumentsFromTemplate(
        workflowTemplate.workflowTemplate as any[],
      );

      // Determine recipient email and name
      const recipientEmail = application.user?.email || application.guest_email;
      const recipientName = application.user?.name || application.guest_name;

      if (!recipientEmail || !recipientName) {
        this.logger.warn(
          `No recipient email or name found for application: ${applicationId}`,
        );
        return;
      }

      // Prepare email data
      const emailData = {
        applicantName: recipientName,
        applicationId: application.application_number,
        serviceName: workflowTemplate.name,
        requiredDocuments: requiredDocuments,
        websiteUrl: `${process.env.WEBSITE}/auth/login`,
        applicationCreatedDate: new Date(),
        isAutoGenerated: true, // This is an automated workflow assignment
      };

      // Send the email
      await this.emailTemplateService.sendApplicationRequirementsEmail(
        recipientEmail,
        emailData,
      );

      this.logger.log(
        `Workflow template assignment email sent successfully to: ${recipientEmail}`,
      );
    } catch (error) {
      this.loggerService.error(
        'Failed to send workflow template assignment email',
        error,
        {
          applicationId,
          workflowTemplateId,
          service: 'ApplicationService',
        },
      );
      // Don't throw error to avoid failing the main workflow assignment operation
    }
  }

  /**
   * Extract required documents from workflow template
   */
  private extractRequiredDocumentsFromTemplate(
    workflowTemplate: any[],
  ): string[] {
    const requiredDocuments: string[] = [];

    if (!workflowTemplate || !Array.isArray(workflowTemplate)) {
      return requiredDocuments;
    }

    for (const stage of workflowTemplate) {
      if (
        stage.documentsRequired &&
        stage.documents &&
        Array.isArray(stage.documents)
      ) {
        for (const document of stage.documents) {
          if (document.required && document.documentName) {
            // Format document name for display
            const formattedName = document.documentName
              .replace(/_/g, ' ')
              .replace(/\b\w/g, (l: string) => l.toUpperCase());
            requiredDocuments.push(formattedName);
          }
        }
      }
    }

    return requiredDocuments.length > 0
      ? requiredDocuments
      : ['Required documents will be specified based on your application'];
  }
}
