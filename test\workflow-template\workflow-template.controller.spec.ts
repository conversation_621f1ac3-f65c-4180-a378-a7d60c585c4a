/**
 * Workflow Template Controller Unit Tests
 *
 * This file contains comprehensive unit tests for the WorkflowTemplateController.
 * Tests cover all REST API endpoints, authentication, validation, and error handling.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-01-06
 */

import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { WorkflowTemplateController } from '../../src/workflow-template/workflow-template.controller';
import { WorkflowTemplateService } from '../../src/workflow-template/workflow-template.service';
import {
  CreateWorkflowTemplateDto,
  UpdateWorkflowTemplateDto,
  WorkflowTemplateFiltersDto,
} from '../../src/workflow-template/dto/workflow-template.dto';

describe('WorkflowTemplateController', () => {
  let controller: WorkflowTemplateController;

  // Mock data
  const mockAdminUser = {
    id: 'admin123',
    email: '<EMAIL>',
    tokenType: 'admin',
    sub: { name: 'Admin User' },
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockWorkflowTemplateResponse = {
    id: 'clx1234567890abcdef',
    name: 'Standard Immigration Workflow Template',
    description: 'Complete workflow template for immigration applications',
    serviceType: 'immigration' as const,
    serviceId: 'immigration_service_123',
    isActive: true,
    workflowTemplate: [
      {
        stageName: 'Document Collection',
        stageOrder: 1,
        documentsRequired: true,
        documents: [
          { documentName: 'passport', required: true },
          { documentName: 'visa', required: true },
        ],
        customFormRequired: true,
        customForm: [
          { fieldName: 'name', fieldType: 'text', required: true },
          { fieldName: 'age', fieldType: 'number', required: true },
        ],
      },
    ],
    createdBy: 'Admin User',
    updatedBy: null,
    createdAt: new Date('2025-01-06T10:30:00.000Z'),
    updatedAt: new Date('2025-01-06T10:30:00.000Z'),
  };

  const mockCreateDto: CreateWorkflowTemplateDto = {
    name: 'Standard Immigration Workflow Template',
    description: 'Complete workflow template for immigration applications',
    serviceType: 'immigration',
    serviceId: 'immigration_service_123',
    isActive: true,
    workflowTemplate: [
      {
        stageName: 'Document Collection',
        stageOrder: 1,
        documentsRequired: true,
        documents: [
          { documentName: 'passport', required: true },
          { documentName: 'visa', required: true },
        ],
        customFormRequired: true,
        customForm: [
          { fieldName: 'name', fieldType: 'text', required: true },
          { fieldName: 'age', fieldType: 'number', required: true },
        ],
      },
    ],
  };

  const mockPaginatedResponse = {
    data: [mockWorkflowTemplateResponse],
    total: 1,
    page: 1,
    limit: 10,
    totalPages: 1,
  };

  const mockUsageResponse = {
    templateId: 'clx1234567890abcdef',
    activeApplications: 2,
    totalApplications: 5,
    canDelete: false,
    usageDetails: [{ serviceType: 'immigration', count: 5 }],
  };

  // Mock service
  const mockWorkflowTemplateService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    checkUsage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkflowTemplateController],
      providers: [
        {
          provide: WorkflowTemplateService,
          useValue: mockWorkflowTemplateService,
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            verify: jest.fn(),
            verifyAsync: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<WorkflowTemplateController>(
      WorkflowTemplateController,
    );

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a workflow template successfully', async () => {
      mockWorkflowTemplateService.create.mockResolvedValue(
        mockWorkflowTemplateResponse,
      );

      const result = await controller.create(mockCreateDto, mockAdminUser);

      expect(mockWorkflowTemplateService.create).toHaveBeenCalledWith(
        mockCreateDto,
        mockAdminUser,
      );
      expect(result).toEqual(mockWorkflowTemplateResponse);
    });

    it('should handle service errors', async () => {
      const error = new Error('Service error');
      mockWorkflowTemplateService.create.mockRejectedValue(error);

      await expect(
        controller.create(mockCreateDto, mockAdminUser),
      ).rejects.toThrow('Service error');
    });
  });

  describe('findAll', () => {
    it('should return paginated workflow templates', async () => {
      const filters: WorkflowTemplateFiltersDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      mockWorkflowTemplateService.findAll.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.findAll(filters);

      expect(mockWorkflowTemplateService.findAll).toHaveBeenCalledWith(filters);
      expect(result).toEqual(mockPaginatedResponse);
    });

    it('should handle search filters', async () => {
      const filters: WorkflowTemplateFiltersDto = {
        search: 'immigration',
        serviceType: 'immigration',
        isActive: true,
        page: 1,
        limit: 10,
      };

      mockWorkflowTemplateService.findAll.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.findAll(filters);

      expect(mockWorkflowTemplateService.findAll).toHaveBeenCalledWith(filters);
      expect(result).toEqual(mockPaginatedResponse);
    });
  });

  describe('findOne', () => {
    it('should return a workflow template by ID', async () => {
      const templateId = 'clx1234567890abcdef';

      mockWorkflowTemplateService.findOne.mockResolvedValue(
        mockWorkflowTemplateResponse,
      );

      const result = await controller.findOne(templateId);

      expect(mockWorkflowTemplateService.findOne).toHaveBeenCalledWith(
        templateId,
      );
      expect(result).toEqual(mockWorkflowTemplateResponse);
    });
  });

  describe('checkUsage', () => {
    it('should return usage information', async () => {
      const templateId = 'clx1234567890abcdef';

      mockWorkflowTemplateService.checkUsage.mockResolvedValue(
        mockUsageResponse,
      );

      const result = await controller.checkUsage(templateId);

      expect(mockWorkflowTemplateService.checkUsage).toHaveBeenCalledWith(
        templateId,
      );
      expect(result).toEqual(mockUsageResponse);
    });
  });

  describe('update', () => {
    it('should update a workflow template successfully', async () => {
      const templateId = 'clx1234567890abcdef';
      const updateDto: UpdateWorkflowTemplateDto = {
        name: 'Updated Template Name',
        description: 'Updated description',
      };

      const updatedResponse = {
        ...mockWorkflowTemplateResponse,
        name: updateDto.name,
        description: updateDto.description,
      };

      mockWorkflowTemplateService.update.mockResolvedValue(updatedResponse);

      const result = await controller.update(
        templateId,
        updateDto,
        mockAdminUser,
      );

      expect(mockWorkflowTemplateService.update).toHaveBeenCalledWith(
        templateId,
        updateDto,
        mockAdminUser,
      );
      expect(result).toEqual(updatedResponse);
    });
  });

  describe('remove', () => {
    it('should delete a workflow template successfully', async () => {
      const templateId = 'clx1234567890abcdef';

      mockWorkflowTemplateService.remove.mockResolvedValue(undefined);

      await controller.remove(templateId, mockAdminUser);

      expect(mockWorkflowTemplateService.remove).toHaveBeenCalledWith(
        templateId,
        mockAdminUser,
      );
    });

    it('should handle service errors during deletion', async () => {
      const templateId = 'clx1234567890abcdef';
      const error = new Error('Cannot delete template in use');

      mockWorkflowTemplateService.remove.mockRejectedValue(error);

      await expect(
        controller.remove(templateId, mockAdminUser),
      ).rejects.toThrow('Cannot delete template in use');
    });
  });

  describe('findAll with serviceId filter', () => {
    it('should handle serviceId filter in findAll', async () => {
      const filters: WorkflowTemplateFiltersDto = {
        serviceId: 'immigration_service_123',
        page: 1,
        limit: 10,
      };

      const mockPaginatedResponse = {
        data: [mockWorkflowTemplateResponse],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockWorkflowTemplateService.findAll.mockResolvedValue(
        mockPaginatedResponse,
      );

      const result = await controller.findAll(filters);

      expect(result).toEqual(mockPaginatedResponse);
      expect(result.data).toHaveLength(1);
      expect(result.data[0].serviceId).toBe(filters.serviceId);

      expect(mockWorkflowTemplateService.findAll).toHaveBeenCalledWith(filters);
    });

    it('should handle validation errors for invalid serviceId', async () => {
      const filters: WorkflowTemplateFiltersDto = {
        serviceId: 'non_existent_service',
      };

      const error = new Error('Service not found');

      mockWorkflowTemplateService.findAll.mockRejectedValue(error);

      await expect(controller.findAll(filters)).rejects.toThrow(
        'Service not found',
      );
    });
  });
});
