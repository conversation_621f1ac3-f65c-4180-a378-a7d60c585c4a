import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/utils/prisma.service';
import { IJWTPayload } from 'src/types/auth';
import { CommentDto } from './dto/comment.dto';

@Injectable()
export class CommentService {
  constructor(private prisma: PrismaService) {}

  async comment(user: IJWTPayload, dto: CommentDto) {
    const data = await this.prisma.comment.create({
      data: {
        ...dto,
        authorId: user.id,
      },
    });

    return data;
  }
  async remove(id: string) {
    const data = await this.prisma.comment.delete({
      where: {
        id,
      },
    });

    return data;
  }
  async getComments(blogId: string, page: number, limit: number) {
    const data = await this.prisma.comment.findMany({
      where: {
        blogId,
        parentId: null,
      },
      include: {
        author: {
          select: {
            name: true,
            image: true,
          },
        },
        replies: {
          include: {
            author: {
              select: {
                name: true,
                image: true,
              },
            },
            replies: {
              // Include nested replies
              include: {
                author: {
                  select: {
                    name: true,
                    image: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return data;
  }
}
