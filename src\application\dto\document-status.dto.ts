/**
 * Document Status Management DTOs
 *
 * This file contains DTOs for document status updates and document requests
 * in the CareerIreland API application system.
 *
 * <AUTHOR> Ireland Development Team
 * @version 1.0.0
 * @since 2025-06-16
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  ValidateIf,
  IsIn,
} from 'class-validator';

/**
 * Document Status Enum for validation
 */
export enum DocumentStatusEnum {
  PENDING = 'Pending',
  UNDER_REVIEW = 'Under_Review',
  APPROVED = 'Approved',
  REJECTED = 'Rejected',
  REQUIRED_REVISION = 'Required_Revision',
  EXPIRED = 'Expired',
  REQUEST = 'Request',
}

/**
 * Statuses that require a reason field
 */
const STATUSES_REQUIRING_REASON = [
  DocumentStatusEnum.REJECTED,
  DocumentStatusEnum.REQUIRED_REVISION,
  DocumentStatusEnum.EXPIRED,
  DocumentStatusEnum.UNDER_REVIEW,
];

/**
 * DTO for updating document status
 */
export class UpdateDocumentStatusDto {
  @ApiProperty({
    description: 'New status for the document',
    enum: DocumentStatusEnum,
    example: DocumentStatusEnum.APPROVED,
  })
  @IsEnum(DocumentStatusEnum)
  @IsNotEmpty()
  status: DocumentStatusEnum;

  @ApiPropertyOptional({
    description:
      'Reason for status change (required for Rejected, Required_Revision, Expired, Under_Review)',
    example: 'Document quality is insufficient, please provide a clearer copy',
  })
  @ValidateIf((o) => STATUSES_REQUIRING_REASON.includes(o.status))
  @IsString()
  @IsNotEmpty()
  reason?: string;

  @ApiPropertyOptional({
    description: 'Additional review comments',
    example: 'Please ensure all pages are clearly visible',
  })
  @IsOptional()
  @IsString()
  reviewComments?: string;
}

/**
 * DTO for requesting a new document
 */
export class RequestDocumentDto {
  @ApiProperty({
    description: 'Name of the document being requested',
    example: 'Updated Passport Copy',
  })
  @IsString()
  @IsNotEmpty()
  documentName: string;

  @ApiProperty({
    description: 'Reason for requesting this document',
    example: 'Current passport copy has expired, need updated version',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiPropertyOptional({
    description: 'Document category for organization',
    example: 'identity_documents',
  })
  @IsOptional()
  @IsString()
  documentCategory?: string;

  @ApiPropertyOptional({
    description: 'Workflow stage order this document belongs to',
    example: 1,
  })
  @IsOptional()
  stageOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether this document is required',
    example: true,
  })
  @IsOptional()
  required?: boolean;
}

/**
 * Response DTO for document status update
 */
export class UpdateDocumentStatusResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Document status updated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Updated document information',
  })
  data: {
    id: string;
    status: string;
    reviewedBy: string;
    reviewedAt: string;
    reviewComments?: string;
    rejectionReason?: string;
  };
}

/**
 * Response DTO for document request
 */
export class RequestDocumentResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Document request created successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Created document request information',
  })
  data: {
    id: string;
    applicationId: string;
    documentName: string;
    status: string;
    requestReason: string;
    requestedBy: string;
    createdAt: string;
  };
}
