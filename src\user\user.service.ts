import {
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateUserDto, LoginDto, UpdateUserDto } from './dto/user.dto';
import { hash } from 'bcrypt';
import { PrismaService } from 'src/utils/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { VerifyOtpDto } from 'src/otp/dto/otp.dto';
import { OtpService } from 'src/otp/otp.service';
import { IJWTPayload } from 'src/types/auth';
import { compare } from 'bcrypt';

const EXPIRE_TIME = 5 * 60 * 60 * 1000;
@Injectable()
export class UserService {
  constructor(
    private prisma: PrismaService,
    private otpService: OtpService,
    private jwtService: JwtService,
  ) {}

  async create(dto: CreateUserDto) {
    const user = await this.prisma.user.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) throw new ConflictException('User Email already exists');

    // Prepare user data with optional password handling
    const userData: any = {
      ...dto,
      provider: 'credentials',
    };

    // Only hash password if provided
    if (dto.password) {
      userData.password = await hash(dto.password, 10);
    } else {
      userData.password = null;
    }

    const newUser = await this.prisma.user.create({
      data: userData,
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = newUser;

    // Conditionally handle OTP email verification based on emailVerified field
    if (!result.emailVerified) {
      // Email not verified - send OTP verification email
      const otp = await this.otpService.generateOTPToken({
        email: result.email,
        url: process.env.VERIFY_CLIENT_EMAIL_URL,
      });
      return {
        token: otp.token,
        status: 'Ok',
        message: 'Verify your email address',
      };
    } else {
      // Email already verified - skip OTP verification
      return {
        status: 'Ok',
        message:
          'Account created successfully. Email verification not required.',
        user: result,
      };
    }
  }

  async removeAccount(user: IJWTPayload) {
    const data = await this.prisma.user.delete({
      where: {
        id: user.id,
      },
    });

    return data;
  }
  async update(user: IJWTPayload, dto: UpdateUserDto) {
    return await this.adminUpdate(user.id, dto);
  }

  async findByEmail(email: string) {
    return await this.prisma.user.findUnique({
      where: {
        email: email,
      },
    });
  }
  async findById(user: IJWTPayload) {
    const data = await this.prisma.user.findUnique({
      where: {
        id: user.id,
      },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        createdAt: true,
        emailVerified: true,
        provider: true,
        updatedAt: true,
        services: {
          where: {
            serviceId: { not: null },
            mentor_services: {
              mentorId: { not: null },
            },
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            mentor_services: {
              where: {
                mentorId: { not: null }, // Exclude reviews where userId is NULL
              },
              select: {
                price: true,
                name: true,
                id: true,
                meeting_link: true,
                mentor: {
                  select: {
                    id: true,
                    name: true,
                    image: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        packages: {
          where: {
            packageId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            package: {
              select: {
                amount: true,
                name: true,
                id: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        immigration_services: {
          where: {
            immigration_serviceId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            immigration_service: {
              select: {
                amount: true,
                name: true,
                id: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        training: {
          where: {
            trainingId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            training: {
              select: {
                amount: true,
                name: true,
                id: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        reviews: {
          where: {
            mentorId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            message: true,
            rating: true,
            createdAt: true,
            mentor: {
              select: {
                id: true,
                email: true,
                image: true,
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });
    const total_spent = await this.prisma.$queryRaw<
      { total_spent: number | null }[]
    >`
  SELECT 
      SUM(amount) AS total_spent
  FROM (
      SELECT amount
      FROM user_mentor_service
      WHERE "userId" = ${user.id} AND status = 'paid'

      UNION ALL

      SELECT amount
      FROM user_package
      WHERE "userId" = ${user.id} AND status = 'paid'

      UNION ALL

      SELECT amount
      FROM user_immigration_service
      WHERE "userId" = ${user.id} AND status = 'paid'

      UNION ALL

      SELECT amount
      FROM user_training
      WHERE "userId" = ${user.id} AND status = 'paid'
  ) AS combined_amounts;
`;

    if (data === null)
      throw new UnauthorizedException(
        'the user with this email does not exits',
      );

    return {
      ...data,
      total_spent: total_spent[0]?.total_spent
        ? total_spent[0]?.total_spent.toString()
        : '0',
    };
  }

  async login(dto: LoginDto) {
    const user = await this.validateUser(dto);
    const payload = {
      id: user.id,
      email: user.email,
      tokenType: 'user',
      sub: {
        name: user.name,
      },
    };

    if (!user.emailVerified) {
      await this.otpService.generateOTPToken({
        email: user.email,
        url: process.env.VERIFY_CLIENT_EMAIL_URL,
      });
      throw new UnauthorizedException(
        'Your email is not verified. A verification code has been sent to your email.',
      );
    }
    return {
      user,
      backendTokens: {
        accessToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtSecretKey,
        }),
        refreshToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtRefreshTokenKey,
        }),
        expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
      },
    };
  }

  async googleLogin(dto: CreateUserDto) {
    const user = await this.prisma.user.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) {
      if (user.provider === 'credentials')
        throw new UnauthorizedException('User already exists');
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user ?? { password: null };
      const payload = {
        id: user.id,
        email: dto.email,
        tokenType: 'user',
        sub: {
          name: dto.name,
        },
      };
      return {
        user: result,
        backendTokens: {
          accessToken: await this.jwtService.signAsync(payload, {
            expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
            secret: process.env.jwtSecretKey,
          }),
          refreshToken: await this.jwtService.signAsync(payload, {
            expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
            secret: process.env.jwtRefreshTokenKey,
          }),
          expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
        },
      };
    }

    const newUser = await this.prisma.user.create({
      data: {
        ...dto,
        provider: 'google',
      },
    });
    if (!newUser) throw new ConflictException();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = newUser ?? { password: null };
    const payload = {
      id: newUser.id,
      email: dto.email,
      tokenType: 'user',
      sub: {
        name: dto.name,
      },
    };
    return {
      user: result,
      backendTokens: {
        accessToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtSecretKey,
        }),
        refreshToken: await this.jwtService.signAsync(payload, {
          expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
          secret: process.env.jwtRefreshTokenKey,
        }),
        expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
      },
    };
  }

  async validateUser(dto: LoginDto) {
    const user = await this.findByEmail(dto.email);
    if (user === null) {
      throw new UnauthorizedException('Incorrect email');
    }
    if (user.password === null)
      throw new UnauthorizedException(
        'Your account was created using a third-party authentication provider (e.g., Google, Facebook). Please log in using the respective provider. Or create a password',
      );
    if (user && (await compare(dto.password, user.password))) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user;
      return result;
    }
    throw new UnauthorizedException('Incorrect password');
  }

  async refreshToken(user: any) {
    const payload = {
      id: user.id,
      email: user.email,
      tokenType: 'user',
      sub: {
        name: user.name,
      },
    };

    return {
      accessToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.ACCESS_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtSecretKey,
      }),
      refreshToken: await this.jwtService.signAsync(payload, {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRY_DATE,
        secret: process.env.jwtRefreshTokenKey,
      }),
      expiresIn: new Date().setTime(new Date().getTime() + EXPIRE_TIME),
    };
  }

  async verifyEmail(dto: VerifyOtpDto) {
    const verify = await this.otpService.verifyOTPToken(dto);
    const user = await this.prisma.user.update({
      where: {
        email: verify.email,
      },
      data: {
        emailVerified: true,
      },
    });
    if (!user) throw new UnauthorizedException('user is not registered');

    return { status: 'OK', message: 'User Email Verified' };
  }

  async getUsers(page: number, limit: number) {
    const isPagination = page > 0 && limit > 0;

    const users = await this.prisma.user.findMany({
      skip: isPagination ? (page - 1) * limit : undefined,
      take: isPagination ? limit : undefined,
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        createdAt: true,
        emailVerified: true,
        provider: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return users;
  }

  async getUserDetailForAdmin(id: string) {
    const user = await this.prisma.user.findUnique({
      where: {
        id,
      },
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        createdAt: true,
        emailVerified: true,
        provider: true,
        updatedAt: true,
        services: {
          where: {
            serviceId: { not: null },
            mentor_services: {
              mentorId: { not: null },
            },
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            mentor_services: {
              select: {
                price: true,
                name: true,
                id: true,
                meeting_link: true,
                mentor: {
                  select: {
                    id: true,
                    name: true,
                    image: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        packages: {
          where: {
            packageId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            package: {
              select: {
                amount: true,
                name: true,
                id: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        immigration_services: {
          where: {
            immigration_serviceId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            immigration_service: {
              select: {
                amount: true,
                name: true,
                id: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        training: {
          where: {
            trainingId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            amount: true,
            status: true,
            progress: true,
            createdAt: true,
            training: {
              select: {
                amount: true,
                name: true,
                id: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        reviews: {
          where: {
            mentorId: { not: null }, // Exclude reviews where userId is NULL
          },
          select: {
            id: true,
            message: true,
            rating: true,
            createdAt: true,
            mentor: {
              select: {
                id: true,
                email: true,
                image: true,
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    const total_spent = await this.prisma.$queryRaw<
      { total_spent: number | null }[]
    >`
    SELECT 
        SUM(amount) AS total_spent
    FROM (
        SELECT amount
        FROM user_mentor_service
        WHERE "userId" = ${id} AND status = 'paid'

        UNION ALL

        SELECT amount
        FROM user_package
        WHERE "userId" = ${id} AND status = 'paid'

        UNION ALL

        SELECT amount
        FROM user_immigration_service
        WHERE "userId" = ${id} AND status = 'paid'

        UNION ALL

        SELECT amount
        FROM user_training
        WHERE "userId" = ${user.id} AND status = 'paid'
    ) AS combined_amounts;
  `;

    return {
      ...user,
      total_spent: total_spent[0]?.total_spent
        ? total_spent[0]?.total_spent.toString()
        : '0',
    };
  }

  async adminRemove(id: string) {
    const data = await this.prisma.user.delete({
      where: {
        id,
      },
    });

    return data;
  }

  async adminRegister(dto: CreateUserDto) {
    const user = await this.prisma.user.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (user) throw new ConflictException('User Email already exists');

    const newUser = await this.prisma.user.create({
      data: {
        ...dto,
        provider: 'credentials',
        emailVerified: true,
        password: await hash(dto.password, 10),
      },
    });

    return newUser;
  }
  async adminUpdate(id: string, dto: UpdateUserDto) {
    let data;

    if (dto?.password) {
      data = await this.prisma.user.update({
        where: {
          id: id,
        },
        data: {
          ...dto,
          password: await hash(dto.password, 10),
        },
      });
    } else {
      data = await this.prisma.user.update({
        where: {
          id: id,
        },
        data: {
          ...dto,
        },
      });
    }

    return data;
  }
}
