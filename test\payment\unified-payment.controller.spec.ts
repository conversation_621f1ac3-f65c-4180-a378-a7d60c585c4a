/**
 * Unified Payment Controller Tests
 *
 * Comprehensive test suite for UnifiedPaymentController.
 * Tests all HTTP endpoints, validation, authentication, and error handling.
 *
 * @desc Tests the unified payment API endpoints that replace 16 legacy endpoints
 * @assumptions Uses mocked UnifiedPaymentService and authentication guards
 * @mocked_dependencies UnifiedPaymentService, JwtGuard, JwtAdmin
 */

// Mock external dependencies
jest.mock('@react-email/components', () => ({
  render: jest.fn().mockReturnValue('<html>Mock Email</html>'),
}));

import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UnifiedPaymentController } from '../../src/payment/unified-payment.controller';
import { UnifiedPaymentService } from '../../src/payment/unified-payment.service';
import { JwtGuard } from '../../src/guards/jwt.guard';
import { JwtAdmin } from '../../src/guards/jwt.admin.guard';
import { JwtAdminOrAgent } from '../../src/guards/jwt.admin-or-agent.guard';
import { mockJWTPayload, resetAllMocks } from '../utils/test-helpers';
import {
  validUserPaymentDto,
  validGuestPaymentDto,
  validPackagePaymentDto,
  invalidPaymentDtos,
  validPaymentFilters,
  emptyPaymentFilters,
  mockPaymentAnalytics,
  mockPaymentHistory,
  errorMessages,
  validAdminProgressDto,
  mockAdminProgressResponse,
  strictGuestPaymentDto,
  strictUserPaymentDto,
} from '../fixtures/payment-fixtures';
import { PaymentType, ServiceType } from '../../src/payment/dto/payment.dto';

describe('UnifiedPaymentController', () => {
  let controller: UnifiedPaymentController;
  let service: UnifiedPaymentService;

  // Mock service implementation
  const mockUnifiedPaymentService = {
    createPayment: jest.fn(),
    processWebhook: jest.fn(),
    getPaymentHistory: jest.fn(),
    getPaymentAnalytics: jest.fn(),
    updatePaymentProgress: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UnifiedPaymentController],
      providers: [
        {
          provide: UnifiedPaymentService,
          useValue: mockUnifiedPaymentService,
        },
      ],
    })
      .overrideGuard(JwtGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(JwtAdmin)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(JwtAdminOrAgent)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<UnifiedPaymentController>(UnifiedPaymentController);
    service = module.get<UnifiedPaymentService>(UnifiedPaymentService);

    // Reset all mocks before each test
    resetAllMocks();
  });

  afterEach(() => {
    resetAllMocks();
  });

  describe('createPayment', () => {
    const mockPaymentResponse = {
      status: 'success',
      url: 'https://checkout.stripe.com/pay/cs_test_123',
      paymentId: 'payment_test_123',
    };

    describe('User Payments (Authenticated)', () => {
      it('should create payment for authenticated user with valid service', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        const result = await controller.createPayment(
          mockJWTPayload,
          strictUserPaymentDto,
        );

        // Assert
        expect(result).toEqual(mockPaymentResponse);
        expect(service.createPayment).toHaveBeenCalledWith(mockJWTPayload, {
          ...strictUserPaymentDto,
          paymentType: PaymentType.USER,
        });
        expect(service.createPayment).toHaveBeenCalledTimes(1);
      });

      it('should create payment for package service', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        const result = await controller.createPayment(
          mockJWTPayload,
          validPackagePaymentDto,
        );

        // Assert
        expect(result).toEqual(mockPaymentResponse);
        expect(service.createPayment).toHaveBeenCalledWith(
          mockJWTPayload,
          validPackagePaymentDto,
        );
      });

      it('should handle service errors gracefully', async () => {
        // Arrange
        const serviceError = new NotFoundException(
          errorMessages.serviceNotFound,
        );
        mockUnifiedPaymentService.createPayment.mockRejectedValue(serviceError);

        // Act & Assert
        await expect(
          controller.createPayment(mockJWTPayload, validUserPaymentDto),
        ).rejects.toThrow(NotFoundException);
        expect(service.createPayment).toHaveBeenCalledWith(
          mockJWTPayload,
          validUserPaymentDto,
        );
      });
    });

    describe('Guest Payments (Unauthenticated)', () => {
      it('should create payment for guest user with valid data', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        const result = await controller.createGuestPayment(
          strictGuestPaymentDto,
        );

        // Assert
        expect(result).toEqual(mockPaymentResponse);
        expect(service.createPayment).toHaveBeenCalledWith(null, {
          serviceType: strictGuestPaymentDto.serviceType,
          serviceId: strictGuestPaymentDto.serviceId,
          paymentType: PaymentType.GUEST,
          name: strictGuestPaymentDto.name,
          email: strictGuestPaymentDto.email,
          mobile: strictGuestPaymentDto.mobile,
        });
      });

      it('should enforce strict guest payment data structure', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        const result = await controller.createGuestPayment({
          name: 'Test Guest',
          email: '<EMAIL>',
          mobile: '1234567890',
          serviceType: ServiceType.SERVICE,
          serviceId: 'test_service_id',
        });

        // Assert
        expect(result).toEqual(mockPaymentResponse);
        expect(service.createPayment).toHaveBeenCalledWith(null, {
          serviceType: ServiceType.SERVICE,
          serviceId: 'test_service_id',
          paymentType: PaymentType.GUEST,
          name: 'Test Guest',
          email: '<EMAIL>',
          mobile: '1234567890',
        });
      });
    });

    describe('Authentication Validation', () => {
      it('should throw BadRequestException when user payment attempted without authentication', async () => {
        // Arrange
        const userPaymentDto = {
          ...validUserPaymentDto,
          paymentType: PaymentType.USER,
        };

        // Act & Assert
        await expect(
          controller.createPayment(null, userPaymentDto),
        ).rejects.toThrow(BadRequestException);

        expect(service.createPayment).not.toHaveBeenCalled();
      });

      it('should allow guest payment without authentication', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        const result = await controller.createGuestPayment(
          strictGuestPaymentDto,
        );

        // Assert
        expect(result).toEqual(mockPaymentResponse);
        expect(service.createPayment).toHaveBeenCalledWith(null, {
          serviceType: strictGuestPaymentDto.serviceType,
          serviceId: strictGuestPaymentDto.serviceId,
          paymentType: PaymentType.GUEST,
          name: strictGuestPaymentDto.name,
          email: strictGuestPaymentDto.email,
          mobile: strictGuestPaymentDto.mobile,
        });
      });
    });

    describe('Input Validation Edge Cases', () => {
      it('should handle empty service ID', async () => {
        // Arrange
        const invalidDto = { ...validUserPaymentDto, serviceId: '' };

        // Act & Assert
        // In real scenario, class-validator would catch this
        // Testing controller behavior assuming validation passes
        await expect(
          controller.createPayment(mockJWTPayload, invalidDto),
        ).resolves.toBeDefined();
      });

      it('should handle invalid service type', async () => {
        // Arrange
        const serviceError = new BadRequestException(
          errorMessages.invalidPaymentData,
        );
        mockUnifiedPaymentService.createPayment.mockRejectedValue(serviceError);

        // Act & Assert
        await expect(
          controller.createPayment(
            mockJWTPayload,
            invalidPaymentDtos.invalidServiceType as any,
          ),
        ).rejects.toThrow(BadRequestException);
      });
    });

    describe('Strict Data Structure Validation', () => {
      it('should enforce strict user payment structure with only serviceType and serviceId', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        const result = await controller.createPayment(
          mockJWTPayload,
          strictUserPaymentDto,
        );

        // Assert
        expect(result).toEqual(mockPaymentResponse);
        expect(service.createPayment).toHaveBeenCalledWith(mockJWTPayload, {
          serviceType: strictUserPaymentDto.serviceType,
          serviceId: strictUserPaymentDto.serviceId,
          paymentType: PaymentType.USER,
        });
      });

      it('should enforce strict guest payment structure with all required fields', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        const result = await controller.createGuestPayment(
          strictGuestPaymentDto,
        );

        // Assert
        expect(result).toEqual(mockPaymentResponse);
        expect(service.createPayment).toHaveBeenCalledWith(null, {
          serviceType: strictGuestPaymentDto.serviceType,
          serviceId: strictGuestPaymentDto.serviceId,
          paymentType: PaymentType.GUEST,
          name: strictGuestPaymentDto.name,
          email: strictGuestPaymentDto.email,
          mobile: strictGuestPaymentDto.mobile,
        });
      });

      it('should force paymentType to user in controller for authenticated endpoint', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        await controller.createPayment(mockJWTPayload, strictUserPaymentDto);

        // Assert
        expect(service.createPayment).toHaveBeenCalledWith(
          mockJWTPayload,
          expect.objectContaining({
            paymentType: PaymentType.USER,
          }),
        );
      });

      it('should force paymentType to guest in controller for guest endpoint', async () => {
        // Arrange
        mockUnifiedPaymentService.createPayment.mockResolvedValue(
          mockPaymentResponse,
        );

        // Act
        await controller.createGuestPayment(strictGuestPaymentDto);

        // Assert
        expect(service.createPayment).toHaveBeenCalledWith(
          null,
          expect.objectContaining({
            paymentType: PaymentType.GUEST,
          }),
        );
      });
    });
  });

  describe('handleWebhook', () => {
    const mockWebhookRequest = {
      body: Buffer.from('webhook_payload'),
      headers: {
        'stripe-signature': 'test_signature',
      },
    } as any;

    const mockWebhookResponse = {
      status: 'success',
      message: 'Webhook processed successfully',
    };

    it('should process valid webhook successfully', async () => {
      // Arrange
      mockUnifiedPaymentService.processWebhook.mockResolvedValue(
        mockWebhookResponse,
      );

      // Act
      const result = await controller.handleWebhook(mockWebhookRequest);

      // Assert
      expect(result).toEqual(mockWebhookResponse);
      expect(service.processWebhook).toHaveBeenCalledWith(mockWebhookRequest);
      expect(service.processWebhook).toHaveBeenCalledTimes(1);
    });

    it('should handle webhook processing errors', async () => {
      // Arrange
      const webhookError = new BadRequestException('Invalid webhook signature');
      mockUnifiedPaymentService.processWebhook.mockRejectedValue(webhookError);

      // Act & Assert
      await expect(
        controller.handleWebhook(mockWebhookRequest),
      ).rejects.toThrow(BadRequestException);
      expect(service.processWebhook).toHaveBeenCalledWith(mockWebhookRequest);
    });

    it('should handle malformed webhook request', async () => {
      // Arrange
      const malformedRequest = { body: null, headers: {} } as any;
      const webhookError = new BadRequestException('Malformed webhook request');
      mockUnifiedPaymentService.processWebhook.mockRejectedValue(webhookError);

      // Act & Assert
      await expect(controller.handleWebhook(malformedRequest)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getPaymentHistory', () => {
    it('should return payment history with filters', async () => {
      // Arrange
      mockUnifiedPaymentService.getPaymentHistory.mockResolvedValue(
        mockPaymentHistory,
      );

      // Act
      const result = await controller.getPaymentHistory(validPaymentFilters);

      // Assert
      expect(result).toEqual(mockPaymentHistory);
      expect(service.getPaymentHistory).toHaveBeenCalledWith(
        validPaymentFilters,
      );
      expect(service.getPaymentHistory).toHaveBeenCalledTimes(1);
    });

    it('should return payment history without filters', async () => {
      // Arrange
      mockUnifiedPaymentService.getPaymentHistory.mockResolvedValue(
        mockPaymentHistory,
      );

      // Act
      const result = await controller.getPaymentHistory(emptyPaymentFilters);

      // Assert
      expect(result).toEqual(mockPaymentHistory);
      expect(service.getPaymentHistory).toHaveBeenCalledWith(
        emptyPaymentFilters,
      );
    });

    it('should handle empty payment history', async () => {
      // Arrange
      mockUnifiedPaymentService.getPaymentHistory.mockResolvedValue([]);

      // Act
      const result = await controller.getPaymentHistory(validPaymentFilters);

      // Assert
      expect(result).toEqual([]);
      expect(service.getPaymentHistory).toHaveBeenCalledWith(
        validPaymentFilters,
      );
    });

    it('should handle service errors in payment history', async () => {
      // Arrange
      const serviceError = new Error(errorMessages.databaseError);
      mockUnifiedPaymentService.getPaymentHistory.mockRejectedValue(
        serviceError,
      );

      // Act & Assert
      await expect(
        controller.getPaymentHistory(validPaymentFilters),
      ).rejects.toThrow(Error);
      expect(service.getPaymentHistory).toHaveBeenCalledWith(
        validPaymentFilters,
      );
    });
  });

  describe('getPaymentAnalytics', () => {
    it('should return payment analytics successfully', async () => {
      // Arrange
      mockUnifiedPaymentService.getPaymentAnalytics.mockResolvedValue(
        mockPaymentAnalytics,
      );

      // Act
      const result = await controller.getPaymentAnalytics();

      // Assert
      expect(result).toEqual(mockPaymentAnalytics);
      expect(service.getPaymentAnalytics).toHaveBeenCalledTimes(1);
    });

    it('should handle analytics service errors', async () => {
      // Arrange
      const serviceError = new Error(errorMessages.databaseError);
      mockUnifiedPaymentService.getPaymentAnalytics.mockRejectedValue(
        serviceError,
      );

      // Act & Assert
      await expect(controller.getPaymentAnalytics()).rejects.toThrow(Error);
      expect(service.getPaymentAnalytics).toHaveBeenCalledTimes(1);
    });

    it('should return analytics with zero values when no payments exist', async () => {
      // Arrange
      const emptyAnalytics = {
        totalRevenue: 0,
        totalPayments: 0,
        paymentsByType: [],
        paymentsByService: [],
        recentPayments: [],
      };
      mockUnifiedPaymentService.getPaymentAnalytics.mockResolvedValue(
        emptyAnalytics,
      );

      // Act
      const result = await controller.getPaymentAnalytics();

      // Assert
      expect(result).toEqual(emptyAnalytics);
      expect(result.totalRevenue).toBe(0);
      expect(result.totalPayments).toBe(0);
    });
  });

  // ========================================
  // ADMIN PAYMENT ENDPOINTS TESTS
  // ========================================

  describe('Admin Payment Progress Update', () => {
    it('should update payment progress successfully', async () => {
      // Arrange
      mockUnifiedPaymentService.updatePaymentProgress.mockResolvedValue(
        mockAdminProgressResponse,
      );

      // Act
      const result = await controller.updatePaymentProgress(
        validAdminProgressDto,
      );

      // Assert
      expect(result).toEqual(mockAdminProgressResponse);
      expect(service.updatePaymentProgress).toHaveBeenCalledWith(
        validAdminProgressDto,
      );
      expect(service.updatePaymentProgress).toHaveBeenCalledTimes(1);
    });

    it('should handle invalid payment ID', async () => {
      // Arrange
      const error = new BadRequestException(errorMessages.invalidPaymentId);
      mockUnifiedPaymentService.updatePaymentProgress.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updatePaymentProgress({
          paymentId: '',
          progress: validAdminProgressDto.progress,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle payment not found', async () => {
      // Arrange
      const error = new NotFoundException(errorMessages.paymentNotFound);
      mockUnifiedPaymentService.updatePaymentProgress.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updatePaymentProgress(validAdminProgressDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle service errors gracefully', async () => {
      // Arrange
      const error = new Error(errorMessages.databaseError);
      mockUnifiedPaymentService.updatePaymentProgress.mockRejectedValue(error);

      // Act & Assert
      await expect(
        controller.updatePaymentProgress(validAdminProgressDto),
      ).rejects.toThrow(Error);
    });
  });

  describe('createMultiMethodPayment', () => {
    const validMultiMethodPaymentDto = {
      amount: 150,
      user_id: 'user123',
      service_type: 'immigration',
      immigration_service_id: 'imm_service_123',
      discount_amount: 50,
      actual_amount: 200,
      payment_method: 'stripe',
    };

    const mockStripeResponse = {
      payment_id: 'pay_123456789',
      stripe_link: 'https://checkout.stripe.com/pay/cs_test_...',
    };

    const mockNonStripeResponse = {
      payment_id: 'pay_123456789',
    };

    beforeEach(() => {
      mockUnifiedPaymentService.createMultiMethodPayment = jest.fn();
    });

    describe('Stripe Payment Method', () => {
      it('should create stripe payment and return payment ID with stripe link', async () => {
        // Arrange
        const stripeDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'stripe',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockResolvedValue(
          mockStripeResponse,
        );

        // Act
        const result = await controller.createMultiMethodPayment(stripeDto);

        // Assert
        expect(
          mockUnifiedPaymentService.createMultiMethodPayment,
        ).toHaveBeenCalledWith(stripeDto);
        expect(result).toEqual(mockStripeResponse);
        expect(result.payment_id).toBeDefined();
        expect(result.stripe_link).toBeDefined();
      });
    });

    describe('Non-Stripe Payment Methods', () => {
      it('should create cash payment and return only payment ID', async () => {
        // Arrange
        const cashDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'cash',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockResolvedValue(
          mockNonStripeResponse,
        );

        // Act
        const result = await controller.createMultiMethodPayment(cashDto);

        // Assert
        expect(
          mockUnifiedPaymentService.createMultiMethodPayment,
        ).toHaveBeenCalledWith(cashDto);
        expect(result).toEqual(mockNonStripeResponse);
        expect(result.payment_id).toBeDefined();
        expect(result.stripe_link).toBeUndefined();
      });

      it('should create bank_deposit payment and return only payment ID', async () => {
        // Arrange
        const bankDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'bank_deposit',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockResolvedValue(
          mockNonStripeResponse,
        );

        // Act
        const result = await controller.createMultiMethodPayment(bankDto);

        // Assert
        expect(
          mockUnifiedPaymentService.createMultiMethodPayment,
        ).toHaveBeenCalledWith(bankDto);
        expect(result).toEqual(mockNonStripeResponse);
        expect(result.payment_id).toBeDefined();
        expect(result.stripe_link).toBeUndefined();
      });

      it('should create online_transfer payment and return only payment ID', async () => {
        // Arrange
        const transferDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'online_transfer',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockResolvedValue(
          mockNonStripeResponse,
        );

        // Act
        const result = await controller.createMultiMethodPayment(transferDto);

        // Assert
        expect(
          mockUnifiedPaymentService.createMultiMethodPayment,
        ).toHaveBeenCalledWith(transferDto);
        expect(result).toEqual(mockNonStripeResponse);
        expect(result.payment_id).toBeDefined();
        expect(result.stripe_link).toBeUndefined();
      });
    });

    describe('Validation Tests', () => {
      it('should reject payment with invalid payment method', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'invalid_method',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockRejectedValue(
          new BadRequestException('Invalid payment method'),
        );

        // Act & Assert
        await expect(
          controller.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
      });

      it('should reject payment with discount greater than actual amount', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          discount_amount: 250,
          actual_amount: 200,
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockRejectedValue(
          new BadRequestException(
            'Discount amount cannot be greater than actual amount.',
          ),
        );

        // Act & Assert
        await expect(
          controller.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
      });

      it('should reject payment with incorrect amount calculation', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          amount: 100, // Should be 150 (200 - 50)
          discount_amount: 50,
          actual_amount: 200,
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockRejectedValue(
          new BadRequestException(
            'Amount must equal actual amount minus discount amount.',
          ),
        );

        // Act & Assert
        await expect(
          controller.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
      });

      it('should reject payment with non-existent user', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          user_id: 'non_existent_user',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockRejectedValue(
          new BadRequestException(
            'User not found. Please ensure you are properly registered.',
          ),
        );

        // Act & Assert
        await expect(
          controller.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
      });

      it('should reject payment with non-existent immigration service', async () => {
        // Arrange
        const invalidDto = {
          ...validMultiMethodPaymentDto,
          immigration_service_id: 'non_existent_service',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockRejectedValue(
          new BadRequestException('Immigration service not found.'),
        );

        // Act & Assert
        await expect(
          controller.createMultiMethodPayment(invalidDto),
        ).rejects.toThrow(BadRequestException);
      });
    });

    describe('Error Handling', () => {
      it('should handle service errors gracefully', async () => {
        // Arrange
        mockUnifiedPaymentService.createMultiMethodPayment.mockRejectedValue(
          new Error('Database connection failed'),
        );

        // Act & Assert
        await expect(
          controller.createMultiMethodPayment(validMultiMethodPaymentDto),
        ).rejects.toThrow('Database connection failed');
      });

      it('should handle Stripe API errors for stripe payments', async () => {
        // Arrange
        const stripeDto = {
          ...validMultiMethodPaymentDto,
          payment_method: 'stripe',
        };
        mockUnifiedPaymentService.createMultiMethodPayment.mockRejectedValue(
          new BadRequestException('Stripe API error: Invalid currency'),
        );

        // Act & Assert
        await expect(
          controller.createMultiMethodPayment(stripeDto),
        ).rejects.toThrow(BadRequestException);
      });
    });
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      // Act
      const result = await controller.healthCheck();

      // Assert
      expect(result).toBeDefined();
      expect(result.status).toBe('OK');
      expect(result.service).toBe('unified-payment-service');
      expect(result.version).toBe('2.0.0');
      expect(result.timestamp).toBeDefined();
    });
  });
});
