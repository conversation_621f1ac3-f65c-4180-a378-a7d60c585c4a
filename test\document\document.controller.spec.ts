import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { DocumentController } from '../../src/application/controllers/document.controller';
import { DocumentVaultService } from '../../src/application/services/document-vault.service';
import { DocumentProcessingService } from '../../src/application/services/document-processing.service';
import { DocumentClassificationService } from '../../src/application/services/document-classification.service';
import { NotificationService } from '../../src/application/services/notification.service';
import { PrismaService } from '../../src/utils/prisma.service';
import { DocumentType } from '@prisma/client';
import { IJWTPayload } from '../../src/types/auth';

describe('DocumentController', () => {
  let controller: DocumentController;
  let documentVaultService: jest.Mocked<DocumentVaultService>;
  let documentProcessingService: jest.Mocked<DocumentProcessingService>;
  let documentClassificationService: jest.Mocked<DocumentClassificationService>;
  let notificationService: jest.Mocked<NotificationService>;
  let prismaService: jest.Mocked<PrismaService>;

  const mockUser: IJWTPayload = {
    id: 'user-123',
    email: '<EMAIL>',
    sub: { name: 'Test User' },
    iat: 1234567890,
    exp: 1234567890,
    tokenType: 'user',
  };

  const mockFile: Express.Multer.File = {
    fieldname: 'file',
    originalname: 'test-document.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024 * 1024,
    buffer: Buffer.from('mock file content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  };

  const mockDocument = {
    id: 'doc-123',
    document_name: 'Test Document',
    original_filename: 'test-document.pdf',
    document_type: 'Passport',
    document_category: 'Identity',
    file_path: 'documents/test-document.pdf',
    file_size: 1024 * 1024,
    user_id: 'user-123',
    guest_email: null,
    expiry_date: null,
    expiry_reminder_sent: false,
    auto_renewal_enabled: false,
    uploaded_by: 'user-123',
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const mockDocumentVaultService = {
      uploadDocument: jest.fn(),
      getUserVault: jest.fn(),
      getUserVaultLegacy: jest.fn(),
    };

    const mockDocumentProcessingService = {
      processDocumentForSearch: jest.fn(),
    };

    const mockDocumentClassificationService = {
      classifyDocument: jest.fn(),
    };

    const mockNotificationService = {
      sendNotification: jest.fn(),
    };

    const mockPrismaService = {
      document_vault: {
        findUnique: jest.fn(),
        update: jest.fn(),
      },
      user: {
        findUnique: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [DocumentController],
      providers: [
        {
          provide: DocumentVaultService,
          useValue: mockDocumentVaultService,
        },
        {
          provide: DocumentProcessingService,
          useValue: mockDocumentProcessingService,
        },
        {
          provide: DocumentClassificationService,
          useValue: mockDocumentClassificationService,
        },
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    controller = module.get<DocumentController>(DocumentController);
    documentVaultService = module.get(DocumentVaultService);
    documentProcessingService = module.get(DocumentProcessingService);
    documentClassificationService = module.get(DocumentClassificationService);
    notificationService = module.get(NotificationService);
    prismaService = module.get(PrismaService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('uploadDocument', () => {
    const uploadDto = {
      document_name: 'Test Document',
      document_type: DocumentType.Passport,
      document_category: 'Identity',
      tags: ['passport', 'identity'],
    };

    beforeEach(() => {
      documentVaultService.uploadDocument.mockResolvedValue(mockDocument);
      documentProcessingService.processDocumentForSearch.mockResolvedValue();
    });

    it('should upload document successfully', async () => {
      const result = await controller.uploadDocument(
        mockFile,
        uploadDto,
        mockUser,
      );

      expect(result).toEqual({
        status: 'success',
        message: 'Document uploaded successfully',
        data: mockDocument,
      });

      expect(documentVaultService.uploadDocument).toHaveBeenCalledWith(
        mockFile,
        expect.objectContaining({
          document_name: uploadDto.document_name,
          document_type: uploadDto.document_type,
        }),
        mockUser.id,
      );
    });

    it('should auto-classify document when type is Other', async () => {
      const uploadDtoWithOther = {
        ...uploadDto,
        document_type: DocumentType.Other,
      };
      documentClassificationService.classifyDocument.mockResolvedValue({
        type: DocumentType.Passport,
        confidence: 0.9,
        suggestions: [],
      });

      await controller.uploadDocument(mockFile, uploadDtoWithOther, mockUser);

      expect(
        documentClassificationService.classifyDocument,
      ).toHaveBeenCalledWith(mockFile);
      expect(documentVaultService.uploadDocument).toHaveBeenCalledWith(
        mockFile,
        expect.objectContaining({
          document_type: DocumentType.Passport,
        }),
        mockUser.id,
      );
    });

    it('should throw BadRequestException when no file provided', async () => {
      await expect(
        controller.uploadDocument(null, uploadDto, mockUser),
      ).rejects.toThrow(BadRequestException);
    });

    it('should process document for search indexing asynchronously', async () => {
      await controller.uploadDocument(mockFile, uploadDto, mockUser);

      // Wait a bit for async processing
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(
        documentProcessingService.processDocumentForSearch,
      ).toHaveBeenCalledWith(mockDocument.id, mockFile);
    });
  });

  describe('getUserDocuments (Document Vault)', () => {
    const mockVaultResult = {
      documents: [mockDocument],
      total: 1,
      page: 1,
      limit: 20,
      totalPages: 1,
    };

    beforeEach(() => {
      documentVaultService.getUserVault.mockResolvedValue(mockVaultResult);
    });

    it('should get user document vault with default parameters', async () => {
      const query = {};
      const result = await controller.getUserDocuments(mockUser, query);

      expect(result).toEqual({
        status: 'success',
        data: [mockDocument],
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
        },
      });

      expect(documentVaultService.getUserVault).toHaveBeenCalledWith(
        mockUser.id,
        {},
      );
    });

    it('should apply filters correctly', async () => {
      const query = {
        document_type: 'Passport',
        search: 'test',
        page: 2,
        limit: 10,
      };

      const filteredResult = {
        documents: [mockDocument],
        total: 5,
        page: 2,
        limit: 10,
        totalPages: 1,
      };

      documentVaultService.getUserVault.mockResolvedValue(filteredResult);

      const result = await controller.getUserDocuments(mockUser, query);

      expect(result).toEqual({
        status: 'success',
        data: [mockDocument],
        pagination: {
          page: 2,
          limit: 10,
          total: 5,
          totalPages: 1,
        },
      });

      expect(documentVaultService.getUserVault).toHaveBeenCalledWith(
        mockUser.id,
        {
          document_type: 'Passport',
          search: 'test',
          page: 2,
          limit: 10,
        },
      );
    });

    it('should handle empty results', async () => {
      const emptyResult = {
        documents: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
      };

      documentVaultService.getUserVault.mockResolvedValue(emptyResult);

      const result = await controller.getUserDocuments(mockUser, {});

      expect(result).toEqual({
        status: 'success',
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
        },
      });
    });

    it('should handle service errors', async () => {
      documentVaultService.getUserVault.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(controller.getUserDocuments(mockUser, {})).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('getDocument', () => {
    beforeEach(() => {
      documentVaultService.getUserVaultLegacy.mockResolvedValue([mockDocument]);
    });

    it('should get document details successfully', async () => {
      const result = await controller.getDocument('doc-123', mockUser);

      expect(result).toEqual({
        status: 'success',
        data: mockDocument,
      });

      expect(documentVaultService.getUserVaultLegacy).toHaveBeenCalledWith(
        mockUser.id,
        {},
      );
    });

    it('should throw NotFoundException when document not found', async () => {
      documentVaultService.getUserVaultLegacy.mockResolvedValue([]);

      await expect(controller.getDocument('doc-123', mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle service errors', async () => {
      documentVaultService.getUserVaultLegacy.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(controller.getDocument('doc-123', mockUser)).rejects.toThrow(
        'Database error',
      );
    });
  });
});
