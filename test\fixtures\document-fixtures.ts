import { DocumentType, DocumentStatus } from '@prisma/client';

/**
 * Test fixtures for document management testing
 */

export const mockUsers = {
  testUser: {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
  },
  adminUser: {
    id: 'admin-123',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin',
  },
  guestUser: {
    email: '<EMAIL>',
    name: 'Guest User',
  },
};

export const mockFiles = {
  pdfFile: {
    fieldname: 'file',
    originalname: 'test-document.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 1024 * 1024, // 1MB
    buffer: Buffer.from('mock pdf content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  } as Express.Multer.File,

  wordFile: {
    fieldname: 'file',
    originalname: 'test-document.docx',
    encoding: '7bit',
    mimetype:
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    size: 512 * 1024, // 512KB
    buffer: Buffer.from('mock docx content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  } as Express.Multer.File,

  imageFile: {
    fieldname: 'file',
    originalname: 'test-image.jpg',
    encoding: '7bit',
    mimetype: 'image/jpeg',
    size: 2 * 1024 * 1024, // 2MB
    buffer: Buffer.from('mock image content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  } as Express.Multer.File,

  textFile: {
    fieldname: 'file',
    originalname: 'test-document.txt',
    encoding: '7bit',
    mimetype: 'text/plain',
    size: 1024, // 1KB
    buffer: Buffer.from('This is a test document with sample text content.'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  } as Express.Multer.File,

  largeFile: {
    fieldname: 'file',
    originalname: 'large-document.pdf',
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: 30 * 1024 * 1024, // 30MB (exceeds limit)
    buffer: Buffer.alloc(30 * 1024 * 1024),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  } as Express.Multer.File,

  invalidFile: {
    fieldname: 'file',
    originalname: 'malware.exe',
    encoding: '7bit',
    mimetype: 'application/exe',
    size: 1024,
    buffer: Buffer.from('malicious content'),
    destination: '',
    filename: '',
    path: '',
    stream: null,
  } as Express.Multer.File,
};

export const mockDocuments = {
  passport: {
    id: 'doc-passport-123',
    document_name: 'Test Passport',
    original_filename: 'passport.pdf',
    document_type: DocumentType.Passport,
    document_category: 'Identity',
    file_path: 'documents/passport.pdf',
    file_size: 1024 * 1024,
    file_hash: 'abc123hash',
    mime_type: 'application/pdf',
    status: DocumentStatus.Pending,
    version: '1.0',
    is_current_version: true,
    parent_document_id: null,
    user_id: mockUsers.testUser.id,
    guest_email: null,
    expiry_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    expiry_reminder_sent: false,
    auto_renewal_enabled: false,
    verified_by: null,
    verified_at: null,
    verification_notes: null,
    review_required: false,
    uploaded_by: mockUsers.testUser.id,
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
    tags: ['passport', 'identity', 'travel'],
    metadata: {
      extracted_text: 'PASSPORT United States of America John Doe',
      text_length: 45,
      processed_at: new Date().toISOString(),
    },
    access_permissions: {},
    sharing_settings: {},
  },

  visa: {
    id: 'doc-visa-123',
    document_name: 'Tourist Visa',
    original_filename: 'visa.pdf',
    document_type: DocumentType.Visa,
    document_category: 'Travel',
    file_path: 'documents/visa.pdf',
    file_size: 512 * 1024,
    file_hash: 'def456hash',
    mime_type: 'application/pdf',
    status: DocumentStatus.Approved,
    version: '1.0',
    is_current_version: true,
    parent_document_id: null,
    user_id: mockUsers.testUser.id,
    guest_email: null,
    expiry_date: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months from now
    expiry_reminder_sent: false,
    auto_renewal_enabled: false,
    verified_by: mockUsers.adminUser.id,
    verified_at: new Date(),
    verification_notes: 'Document verified and approved',
    review_required: false,
    uploaded_by: mockUsers.testUser.id,
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
    tags: ['visa', 'travel', 'tourism'],
    metadata: {
      extracted_text: 'VISA Entry Permit Tourist Visa Valid Until',
      text_length: 42,
      processed_at: new Date().toISOString(),
    },
    access_permissions: {},
    sharing_settings: {},
  },

  birthCertificate: {
    id: 'doc-birth-123',
    document_name: 'Birth Certificate',
    original_filename: 'birth-certificate.pdf',
    document_type: DocumentType.Birth_Certificate,
    document_category: 'Identity',
    file_path: 'documents/birth-certificate.pdf',
    file_size: 256 * 1024,
    file_hash: 'ghi789hash',
    mime_type: 'application/pdf',
    status: DocumentStatus.Pending,
    version: '1.0',
    is_current_version: true,
    parent_document_id: null,
    user_id: mockUsers.testUser.id,
    guest_email: null,
    expiry_date: null, // Birth certificates don't expire
    expiry_reminder_sent: false,
    auto_renewal_enabled: false,
    verified_by: null,
    verified_at: null,
    verification_notes: null,
    review_required: true,
    uploaded_by: mockUsers.testUser.id,
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
    tags: ['birth', 'certificate', 'identity'],
    metadata: {
      extracted_text: 'CERTIFICATE OF BIRTH John Doe Born January 1, 1990',
      text_length: 52,
      processed_at: new Date().toISOString(),
    },
    access_permissions: {},
    sharing_settings: {},
  },

  guestDocument: {
    id: 'doc-guest-123',
    document_name: 'Guest Passport',
    original_filename: 'guest-passport.pdf',
    document_type: DocumentType.Passport,
    document_category: 'Identity',
    file_path: 'documents/guest-passport.pdf',
    file_size: 1024 * 1024,
    file_hash: 'jkl012hash',
    mime_type: 'application/pdf',
    status: DocumentStatus.Pending,
    version: '1.0',
    is_current_version: true,
    parent_document_id: null,
    user_id: null,
    guest_email: mockUsers.guestUser.email,
    expiry_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
    expiry_reminder_sent: false,
    auto_renewal_enabled: false,
    verified_by: null,
    verified_at: null,
    verification_notes: null,
    review_required: false,
    uploaded_by: null,
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
    tags: ['passport', 'guest', 'identity'],
    metadata: {},
    access_permissions: {},
    sharing_settings: {},
  },

  expiredDocument: {
    id: 'doc-expired-123',
    document_name: 'Expired Passport',
    original_filename: 'expired-passport.pdf',
    document_type: DocumentType.Passport,
    document_category: 'Identity',
    file_path: 'documents/expired-passport.pdf',
    file_size: 1024 * 1024,
    file_hash: 'mno345hash',
    mime_type: 'application/pdf',
    status: DocumentStatus.Expired,
    version: '1.0',
    is_current_version: true,
    parent_document_id: null,
    user_id: mockUsers.testUser.id,
    guest_email: null,
    expiry_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    expiry_reminder_sent: true,
    auto_renewal_enabled: false,
    verified_by: null,
    verified_at: null,
    verification_notes: null,
    review_required: false,
    uploaded_by: mockUsers.testUser.id,
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
    tags: ['passport', 'expired', 'identity'],
    metadata: {},
    access_permissions: {},
    sharing_settings: {},
  },
};

export const mockApplications = {
  immigrationApp: {
    id: 'app-immigration-123',
    application_number: 'IMM-2024-001',
    application_type: 'immigration',
    service_type: 'visa_application',
    service_id: 'visa-service-123',
    user_id: mockUsers.testUser.id,
    guest_name: null,
    guest_email: null,
    guest_mobile: null,
    status: 'In_Progress',
    current_step_id: 'step-2',
    workflow_template_id: 'template-immigration-123',
    priority_level: 'Medium',
    sla_deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    created_at: new Date(),
    updated_at: new Date(),
    submitted_at: new Date(),
    completed_at: null,
    metadata: {},
  },
};

export const mockUploadDtos = {
  validUpload: {
    document_name: 'Test Document',
    document_type: DocumentType.Passport,
    document_category: 'Identity',
    tags: ['passport', 'identity'],
  },

  uploadWithExpiry: {
    document_name: 'Document with Expiry',
    document_type: DocumentType.Visa,
    document_category: 'Travel',
    expiry_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
    tags: ['visa', 'travel'],
  },

  guestUpload: {
    document_name: 'Guest Document',
    document_type: DocumentType.Passport,
    document_category: 'Identity',
    guest_email: mockUsers.guestUser.email,
    tags: ['passport', 'guest'],
  },

  uploadWithApplication: {
    document_name: 'Application Document',
    document_type: DocumentType.Birth_Certificate,
    document_category: 'Identity',
    application_id: mockApplications.immigrationApp.id,
    tags: ['birth', 'certificate'],
  },
};

export const mockSearchDtos = {
  basicSearch: {
    query: 'passport',
  },

  advancedSearch: {
    query: 'document',
    document_type: DocumentType.Passport,
    status: DocumentStatus.Pending,
    tags: ['passport', 'identity'],
    page: 1,
    limit: 20,
    sort_by: 'date' as const,
    sort_order: 'desc' as const,
    include_content: true,
  },

  dateRangeSearch: {
    query: 'certificate',
    date_from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    date_to: new Date().toISOString(),
    page: 1,
    limit: 10,
  },
};

export const mockClassificationResults = {
  passportClassification: {
    type: DocumentType.Passport,
    confidence: 0.95,
    suggestions: [],
  },

  visaClassification: {
    type: DocumentType.Visa,
    confidence: 0.88,
    suggestions: ['Entry Permit'],
  },

  uncertainClassification: {
    type: DocumentType.Other,
    confidence: 0.3,
    suggestions: ['Passport', 'ID Card', 'Driver License'],
  },
};

export const mockVersionData = {
  versionNotes: 'Updated document with corrections and additional information',
  newVersionFile: mockFiles.pdfFile,
};

export const mockSharingOptions = {
  publicSharing: {
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    permissions: ['read'],
  },

  passwordProtectedSharing: {
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    permissions: ['read'],
    password: 'secure123',
  },

  fullAccessSharing: {
    permissions: ['read', 'write', 'share'],
  },
};
